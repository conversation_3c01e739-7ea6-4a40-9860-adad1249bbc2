# Enterprise KG Hybrid Search - Implementation Guide

## Overview

This guide explains how Enterprise KG implements hybrid search by combining Pinecone semantic search with Neo4j knowledge graph queries, providing comprehensive examples and integration patterns.

## Architecture Overview

```
User Query → Hybrid Search Engine
    ├── Pinecone Semantic Search (Your existing setup)
    │   └── Returns: Document chunks with file_id, org_id, chunk_text
    ├── Neo4j Knowledge Graph Search (New capability)
    │   └── Returns: Structured entity relationships
    └── Result Combiner + LLM Answer Generator
        └── Returns: Enhanced response with context + relationships
```

## Two Hybrid Search Approaches

### 1. Template-Based Approach (Knowledge Enrichment)

**How it works:**
- Detects user intent from query patterns
- Uses predefined Cypher query templates
- Fast and predictable for common query types

**Example Query:** "Who is working on Project Alpha?"

**Process:**
1. **Intent Detection:** WHO_WORKS_ON
2. **Entity Extraction:** ["Project Alpha"]
3. **Template Selection:** person_to_project pattern
4. **Generated Cypher:**
```cypher
MATCH (person:Enti<PERSON>)-[r:INVOLVED_IN|MANAGES|OWNS]->(target:Entity)
WHERE target.name CONTAINS $entity_name 
AND person.type IN $person_types
AND target.type IN $target_types
RETURN person.name, person.type, type(r), target.name, target.type
```

### 2. Entity Discovery Approach

**How it works:**
- Searches Pinecone first to find relevant document chunks
- Extracts entities from returned chunks using LLM
- Queries Neo4j for relationships involving discovered entities
- More adaptive but slower than template approach

**Example Query:** "What systems does Mike Johnson work with?"

**Process:**
1. **Pinecone Search:** Find chunks mentioning "Mike Johnson" and "systems"
2. **Entity Extraction:** Extract entities from chunks: ["Mike Johnson", "CRM System", "Analytics Dashboard"]
3. **Neo4j Query:** Find relationships for each discovered entity
4. **Result Combination:** Merge semantic context with structured relationships

## Detailed Implementation Examples

### Example 1: "Who is working on Project Alpha?"

#### Step 1: Pinecone Semantic Search
```python
# Your existing Pinecone setup (unchanged)
query_embedding = create_embedding("Who is working on Project Alpha?")

pinecone_results = pinecone_client.query(
    vector=query_embedding,
    top_k=10,
    filter={"org_id": "tech_corp_001"},
    include_metadata=True
)

# Results from your existing Pinecone:
semantic_chunks = [
    {
        "id": "chunk_123",
        "score": 0.89,
        "metadata": {
            "file_id": "project_alpha_status.md",
            "org_id": "tech_corp_001", 
            "chunk_text": "John Doe is the project manager for Project Alpha. Sarah Smith leads the development team..."
        }
    },
    {
        "id": "chunk_456", 
        "score": 0.82,
        "metadata": {
            "file_id": "team_assignments.md",
            "org_id": "tech_corp_001",
            "chunk_text": "Project Alpha team includes Mike Johnson (UI Designer) and Lisa Chen (Data Analyst)..."
        }
    }
]
```

#### Step 2: Neo4j Knowledge Graph Query
```python
# Template-based approach
# Intent: WHO_WORKS_ON
# Entity: "Project Alpha"

cypher_query = """
MATCH (person:Entity)-[r:INVOLVED_IN|MANAGES|OWNS|RESPONSIBLE_FOR]->(target:Entity)
WHERE target.name CONTAINS $entity_name 
AND person.type IN $person_types
AND target.type IN $target_types
RETURN person.name as person, person.type as person_type, 
       type(r) as relationship, target.name as target, target.type as target_type
"""

parameters = {
    "entity_name": "Project Alpha",
    "person_types": ["Person", "Employee", "Manager"],
    "target_types": ["Project", "Initiative", "Program"]
}

# Neo4j Results:
structured_relationships = [
    {
        "person": "John Doe",
        "person_type": "Person", 
        "relationship": "MANAGES",
        "target": "Project Alpha",
        "target_type": "Project"
    },
    {
        "person": "Sarah Smith",
        "person_type": "Person",
        "relationship": "INVOLVED_IN", 
        "target": "Project Alpha",
        "target_type": "Project"
    },
    {
        "person": "Mike Johnson",
        "person_type": "Person",
        "relationship": "INVOLVED_IN", 
        "target": "Project Alpha",
        "target_type": "Project"
    }
]
```

#### Step 3: Result Combination
```python
combined_results = {
    "method": "hybrid",
    "query": "Who is working on Project Alpha?",
    "answer": "Based on the available data, several people are working on Project Alpha: John Doe serves as the project manager, Sarah Smith leads the development team, Mike Johnson handles UI design, and Lisa Chen works as a data analyst.",
    
    "semantic_search": {
        "chunks": [
            "John Doe is the project manager for Project Alpha. Sarah Smith leads the development team...",
            "Project Alpha team includes Mike Johnson (UI Designer) and Lisa Chen (Data Analyst)..."
        ],
        "total_chunks": 2
    },
    
    "knowledge_graph": {
        "relationships": [
            {"person": "John Doe", "relationship": "MANAGES", "target": "Project Alpha"},
            {"person": "Sarah Smith", "relationship": "INVOLVED_IN", "target": "Project Alpha"},
            {"person": "Mike Johnson", "relationship": "INVOLVED_IN", "target": "Project Alpha"},
            {"person": "Lisa Chen", "relationship": "INVOLVED_IN", "target": "Project Alpha"}
        ],
        "total_relationships": 4,
        "discovered_entities": ["John Doe", "Sarah Smith", "Mike Johnson", "Lisa Chen", "Project Alpha"]
    },
    
    "source_files": ["project_alpha_status.md", "team_assignments.md"],
    "confidence": 0.85,
    "processing_info": {
        "pinecone_results": 2,
        "neo4j_results": 4,
        "combined_sources": 2
    }
}
```

### Example 2: "What systems does Mike Johnson work with?"

#### Step 1: Pinecone Search
```python
# Finds chunks mentioning Mike Johnson and systems
semantic_chunks = [
    "Mike Johnson collaborates with the Design Department on the new CRM System interface...",
    "The UI team, led by Mike Johnson, integrates with the Analytics Dashboard and Mobile App..."
]
```

#### Step 2: Multi-hop Neo4j Query
```python
# Intent: WHAT_SYSTEMS
# Generated Cypher (handles indirect relationships):
cypher_query = """
MATCH (source:Entity)-[r1:INVOLVED_IN|WORKS_FOR]->(intermediate:Entity)
MATCH (intermediate)-[r2:MENTIONS|USES|INTEGRATES_WITH]->(system:Entity)
WHERE source.name CONTAINS $entity_name
AND system.type IN $system_types
RETURN source.name, intermediate.name, system.name, type(r1), type(r2)

UNION

MATCH (source:Entity)-[r:MENTIONS|USES|INTEGRATES_WITH]->(system:Entity)
WHERE source.name CONTAINS $entity_name
AND system.type IN $system_types
RETURN source.name, null as intermediate, system.name, type(r), null as r2
"""

# Results show both direct and indirect system connections:
structured_relationships = [
    {
        "source.name": "Mike Johnson",
        "intermediate.name": "Design Department", 
        "system.name": "CRM System",
        "type(r1)": "WORKS_FOR",
        "type(r2)": "USES"
    },
    {
        "source.name": "Mike Johnson",
        "intermediate.name": null,
        "system.name": "Analytics Dashboard", 
        "type(r)": "INTEGRATES_WITH",
        "type(r2)": null
    }
]
```

## Integration with Your Existing Pinecone Setup

### Non-Disruptive Integration Pattern
```python
class YourMainProjectIntegration:
    def __init__(self):
        # Keep your existing Pinecone client (unchanged)
        self.existing_pinecone_client = your_pinecone_client
        
        # Add Enterprise KG components
        self.neo4j_client = setup_neo4j()
        self.llm_client = setup_llm()
        
        # Create hybrid search engine
        self.hybrid_search = HybridSearchEngine(
            existing_pinecone_client=self.existing_pinecone_client,
            kg_processor=kg_processor
        )
    
    def enhanced_search(self, query: str, user_org_id: str):
        """Enhanced search combining your existing flow with KG"""
        
        # Your existing search logic (unchanged)
        existing_results = self.your_current_search_method(query, user_org_id)
        
        # Add knowledge graph enhancement
        kg_results = self.hybrid_search.search(
            query=query, 
            org_id=user_org_id, 
            method="hybrid"  # or "auto" for automatic method selection
        )
        
        return {
            "semantic_search": existing_results,    # Your current results
            "knowledge_graph": kg_results,          # New KG insights
            "enhanced_answer": kg_results["answer"] # AI-generated comprehensive answer
        }
```

### Your Existing Data Structure (Preserved)
```python
# Your current Pinecone metadata structure works as-is
{
    "id": "chunk_123",
    "values": [0.1, 0.2, ...],  # Your embeddings
    "metadata": {
        "file_id": "document_001.md",      # ✅ Used by hybrid search
        "org_id": "tech_corp_001",         # ✅ Used for filtering
        "chunk_text": "John Doe manages..." # ✅ Used for entity discovery
    }
}
```

## Search Method Comparison

| Method | Speed | Accuracy | Use Case | Example |
|--------|-------|----------|----------|---------|
| **Pinecone Only** | Fast | Good for similarity | Semantic search | "Find documents about customer satisfaction" |
| **Neo4j Only** | Fast | High for relationships | Structured queries | "Who reports to John Doe?" |
| **Template Hybrid** | Fast | High for patterns | Common query types | "Who works on Project Alpha?" |
| **Discovery Hybrid** | Slower | Very High | Complex queries | "What systems connect to CRM through Mike's projects?" |
| **Auto Hybrid** | Variable | Optimal | General purpose | Any query - system chooses best approach |

## Key Benefits

### 1. Complementary Strengths
- **Pinecone**: Semantic similarity, handles synonyms, fuzzy matching
- **Neo4j**: Explicit relationships, complex traversals, structured data
- **Combined**: Cross-validation and comprehensive coverage

### 2. Enhanced Accuracy
- Results from both systems validate each other
- Semantic + structured approaches catch different aspects
- Confidence scoring based on agreement between systems

### 3. Rich Context
- **Document chunks**: Natural language context from Pinecone
- **Structured relationships**: Precise entity connections from Neo4j
- **Source attribution**: Track information provenance
- **AI-generated answers**: Comprehensive responses using both data sources

## Implementation Files

### Core Files for Hybrid Search
- `hybrid_search_engine.py` - Main hybrid search orchestration
- `knowledge_enrichment.py` - Template-based query building
- `entity_discovery.py` - Entity discovery from Pinecone results
- `hybrid_search_example.py` - Complete integration examples

### Integration Examples
- `integration_example.py` - How to integrate with existing systems
- `quick_start.py` - Simple setup and testing

## Next Steps

1. **Test with minimal package**: Use standalone mode to understand entity extraction
2. **Set up Neo4j**: Populate knowledge graph with your documents
3. **Integrate gradually**: Start with template-based approach for common queries
4. **Expand to discovery**: Add entity discovery for complex queries
5. **Optimize performance**: Tune based on your specific query patterns

The hybrid search gives you **"Google-like semantic search" + "SQL-like structured queries"** in one unified system while preserving your existing Pinecone investment.
