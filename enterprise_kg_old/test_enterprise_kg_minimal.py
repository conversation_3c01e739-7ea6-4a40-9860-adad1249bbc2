#!/usr/bin/env python3
"""
Enterprise KG Minimal - Final Test Script

This script demonstrates the complete chunk-based knowledge graph functionality
of the clean modular enterprise_kg_minimal package.

Features tested:
- Document chunking with hybrid strategy
- File → Chunks → Entities graph structure
- Entity and relationship extraction per chunk
- Neo4j storage with proper relationships

Prerequisites:
1. Neo4j running (local or Aura)
2. Requesty API key in .env file
3. Required packages: neo4j, python-dotenv

Usage:
    python test_enterprise_kg_minimal.py
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_test_document():
    """Create a comprehensive test document for chunking and entity extraction."""
    return """
# TechCorp AI Initiative - Project Atlas

## Executive Summary
Project Atlas is TechCorp's flagship AI initiative led by <PERSON>, Senior Project Manager.
The project is sponsored by <PERSON>, VP of Engineering, and aims to develop an 
AI-driven customer insights platform. The initiative represents a $5M investment in 
artificial intelligence and machine learning capabilities.

## Team Structure and Leadership
The core team consists of several key members across different departments:

<PERSON> serves as the Senior Project Manager and reports directly to <PERSON>.
He has 8 years of experience in project management and specializes in AI initiatives.

Dr. <PERSON> leads the Data Science team and manages the machine learning development.
She holds a PhD in Computer Science from Stanford and previously worked at Google AI.

<PERSON> manages the Engineering team and oversees the technical implementation.
He is responsible for system architecture and leads a team of 12 software engineers.

<PERSON> <PERSON> handles Product Management and coordinates customer requirements gathering.
She works closely with the sales team and manages stakeholder communications.

David Kim leads the DevOps team and manages cloud infrastructure on AWS.
His team ensures scalability and maintains 99.9% uptime for all systems.

## Technology Stack and Architecture
The platform leverages cutting-edge technologies and modern architecture:

The backend uses Python and TensorFlow for machine learning model development.
The team chose TensorFlow over PyTorch due to better production deployment tools.

AWS cloud services provide scalable infrastructure including EC2, S3, and RDS.
The system uses Apache Kafka for real-time data streaming and event processing.

PostgreSQL serves as the primary database for structured data storage.
Redis provides caching capabilities and manages user session data.

Docker and Kubernetes handle containerization and orchestration across environments.
The CI/CD pipeline uses Jenkins and deploys to multiple staging environments.

## Business Impact and Metrics
The Customer Insights Platform is expected to deliver significant business value:

Revenue impact includes an estimated $2M in additional annual revenue.
Customer satisfaction is projected to increase by 25% within the first year.
Marketing campaign effectiveness should improve by 40% through better targeting.
Customer churn reduction of 15% is anticipated through predictive analytics.

The platform will serve over 100,000 active users and process 1TB of data daily.
Response times must remain under 200ms for real-time recommendation features.

## Project Timeline and Milestones
The project follows an agile methodology with quarterly milestones:

Q1 2024 focuses on research, prototyping, and technology evaluation.
The team will complete market analysis and competitive research during this phase.

Q2 2024 involves core development including ML model training and API development.
Database schema design and security framework implementation are key deliverables.

Q3 2024 emphasizes integration testing, performance optimization, and user acceptance testing.
Security audits and compliance reviews will be completed before production deployment.

Q4 2024 targets production deployment, user training, and go-to-market execution.
Post-launch monitoring and support processes will be established during this phase.
"""

def setup_configuration():
    """Setup Neo4j and LLM configuration from environment variables."""
    # Neo4j configuration
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    # LLM configuration - use Requesty with working model
    requesty_key = os.getenv("REQUESTY_API_KEY")
    
    if not requesty_key:
        print("❌ REQUESTY_API_KEY not found in environment variables")
        print("   Please set REQUESTY_API_KEY in your .env file")
        return None
    
    print(f"🔗 Neo4j: {neo4j_uri}")
    print(f"🤖 LLM: Requesty with Claude 3.5 Sonnet")
    
    return {
        "neo4j_uri": neo4j_uri,
        "neo4j_user": neo4j_user,
        "neo4j_password": neo4j_password,
        "llm_provider": "requesty",
        "llm_model": "anthropic/claude-3-5-sonnet-20241022",
        "llm_api_key": requesty_key
    }

def clear_previous_test_data(config):
    """Clear any previous test data from Neo4j."""
    try:
        from neo4j import GraphDatabase
        
        driver = GraphDatabase.driver(
            config["neo4j_uri"], 
            auth=(config["neo4j_user"], config["neo4j_password"])
        )
        
        with driver.session() as session:
            # Remove previous test data
            session.run('MATCH (f:File {id: "techcorp_atlas_test"}) DETACH DELETE f')
            session.run('MATCH (c:Chunk) WHERE c.id STARTS WITH "techcorp_atlas_test" DETACH DELETE c')
            
        driver.close()
        print("🧹 Cleared previous test data")
        return True
        
    except Exception as e:
        print(f"⚠️  Could not clear previous data: {e}")
        return True  # Continue anyway

def run_test(config):
    """Run the complete enterprise KG minimal test."""
    
    file_id = "techcorp_atlas_test"
    document_content = create_test_document()
    
    print(f"\n📄 Test Document:")
    print(f"   File ID: {file_id}")
    print(f"   Content length: {len(document_content)} characters")
    print(f"   Expected chunks: ~{len(document_content) // 1000} (with 1000 char chunks)")
    
    print(f"\n🔄 Processing document with enterprise_kg_minimal...")
    
    try:
        from enterprise_kg_minimal import process_document
        
        result = process_document(
            file_id=file_id,
            file_content=document_content,
            neo4j_uri=config["neo4j_uri"],
            neo4j_user=config["neo4j_user"],
            neo4j_password=config["neo4j_password"],
            llm_provider=config["llm_provider"],
            llm_model=config["llm_model"],
            llm_api_key=config["llm_api_key"],
            chunking_strategy="hybrid",
            chunk_size=800,  # Smaller chunks for better entity extraction
            chunk_overlap=150
        )
        
        return result
        
    except Exception as e:
        print(f"❌ Processing failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def display_results(result):
    """Display comprehensive test results."""
    
    if not result:
        print("❌ No results to display")
        return False
    
    print(f"\n📊 Processing Results:")
    print("=" * 50)
    
    if result["success"]:
        print(f"✅ SUCCESS!")
        print(f"   📄 File ID: {result['file_id']}")
        print(f"   🧩 Chunks created: {result['chunks_created']}")
        print(f"   ✅ Chunks processed: {result['chunks_processed']}")
        print(f"   ❌ Chunks failed: {result['chunks_failed']}")
        print(f"   👥 Total entities: {result['total_entities']}")
        print(f"   🔗 Total relationships: {result['total_relationships']}")
        print(f"   📁 File node created: {result['file_node_created']}")
        print(f"   🔗 CONTAINS relationships: {result['contains_relationships_created']}")
        
        # Show detailed chunk results
        print(f"\n📋 Chunk Processing Details:")
        successful_chunks = 0
        for i, chunk_detail in enumerate(result['chunk_details']):
            status = "✅" if chunk_detail['graph_stored'] else "❌"
            entities = chunk_detail['entities_extracted']
            relationships = chunk_detail['relationships_extracted']
            
            print(f"   {status} Chunk {i}: {entities} entities, {relationships} relationships")
            
            if chunk_detail['graph_stored']:
                successful_chunks += 1
            
            if chunk_detail['error']:
                print(f"      ⚠️  Error: {chunk_detail['error']}")
        
        # Calculate success rate
        success_rate = (successful_chunks / result['chunks_created']) * 100 if result['chunks_created'] > 0 else 0
        print(f"\n📈 Success Rate: {success_rate:.1f}% ({successful_chunks}/{result['chunks_created']} chunks)")
        
        return True
    else:
        print(f"❌ FAILED: {result['error']}")
        return False

def show_neo4j_queries(file_id):
    """Show Neo4j Browser queries to explore the results."""
    
    print(f"\n🔍 Neo4j Browser Queries:")
    print("=" * 50)
    print("Copy and paste these queries into Neo4j Browser to explore the graph:\n")
    
    print("1️⃣ View complete File → Chunks → Entities structure:")
    print(f'MATCH (f:File {{id: "{file_id}"}})-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)')
    print('RETURN f, c, e')
    print()
    
    print("2️⃣ View chunks with entity counts:")
    print(f'MATCH (f:File {{id: "{file_id}"}})-[:CONTAINS]->(c:Chunk)')
    print('OPTIONAL MATCH (c)-[:EXTRACTED_FROM]->(e:Entity)')
    print('RETURN c.id, c.chunk_index, c.word_count, count(e) as entity_count')
    print('ORDER BY c.chunk_index')
    print()
    
    print("3️⃣ View entity relationships:")
    print('MATCH (e1:Entity)-[r]->(e2:Entity)')
    print('WHERE type(r) <> "EXTRACTED_FROM"')
    print('RETURN e1.name, type(r), e2.name, r.confidence_score')
    print()
    
    print("4️⃣ View entities by type:")
    print('MATCH (e:Entity)')
    print('RETURN e.entity_type, count(e) as count')
    print('ORDER BY count DESC')
    print()
    
    print("5️⃣ View chunk text previews:")
    print(f'MATCH (c:Chunk) WHERE c.id STARTS WITH "{file_id}"')
    print('RETURN c.chunk_index, substring(c.text, 0, 100) + "..." as preview')
    print('ORDER BY c.chunk_index')

def main():
    """Main test function."""
    
    print("🧪 Enterprise KG Minimal - Final Test")
    print("=" * 60)
    
    # Setup configuration
    config = setup_configuration()
    if not config:
        return False
    
    # Clear previous test data
    clear_previous_test_data(config)
    
    # Run the test
    result = run_test(config)
    
    # Display results
    success = display_results(result)
    
    if success:
        show_neo4j_queries("techcorp_atlas_test")
        
        print(f"\n🎉 Test completed successfully!")
        print("   Open Neo4j Browser and run the queries above to explore the graph")
        print("   The enterprise_kg_minimal package is ready for dev team integration!")
    else:
        print(f"\n💥 Test failed. Check the error messages above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
