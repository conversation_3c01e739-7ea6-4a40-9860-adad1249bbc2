#!/usr/bin/env python3
"""
Test Graph Connectivity and Data Integrity for Enterprise KG

This script tests the graph structure, data integrity, and connectivity
patterns to ensure the knowledge graph is properly constructed.
"""

import os
import sys
from typing import List, Dict, Set, Tuple
from dotenv import load_dotenv

# Add the enterprise_kg_minimal to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'enterprise_kg_minimal'))

load_dotenv()

def test_graph_connectivity():
    """Test graph connectivity and structure."""
    print("\n🔗 Testing Graph Connectivity")
    print("=" * 50)
    
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'neo4j')
        password = os.getenv('NEO4J_PASSWORD', 'password')
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            # Test 1: Check for isolated nodes
            print("\n🔍 Checking for isolated nodes...")
            
            isolated_query = """
            MATCH (n)
            WHERE NOT (n)--()
            RETURN labels(n) as labels, count(n) as count
            """
            
            result = session.run(isolated_query)
            isolated_nodes = list(result)
            
            if isolated_nodes:
                print("   ⚠️  Found isolated nodes:")
                for record in isolated_nodes:
                    labels = ':'.join(record['labels']) if record['labels'] else 'No Label'
                    print(f"      {labels}: {record['count']} nodes")
            else:
                print("   ✅ No isolated nodes found")
            
            # Test 2: Check connectivity between chunks and entities
            print("\n🔍 Checking chunk-entity connectivity...")
            
            chunk_entity_query = """
            MATCH (c:Chunk)-[r]-(e)
            WHERE NOT e:Chunk AND NOT e:File
            RETURN type(r) as relationship_type, labels(e) as entity_labels, count(*) as count
            ORDER BY count DESC
            LIMIT 10
            """
            
            result = session.run(chunk_entity_query)
            chunk_connections = list(result)
            
            if chunk_connections:
                print("   ✅ Chunk-entity connections found:")
                for record in chunk_connections:
                    entity_labels = ':'.join(record['entity_labels'])
                    print(f"      {record['relationship_type']} -> {entity_labels}: {record['count']}")
            else:
                print("   ⚠️  No chunk-entity connections found")
            
            # Test 3: Check entity-entity relationships
            print("\n🔍 Checking entity-entity relationships...")
            
            entity_entity_query = """
            MATCH (e1)-[r]->(e2)
            WHERE NOT e1:Chunk AND NOT e1:File AND NOT e2:Chunk AND NOT e2:File
            RETURN type(r) as relationship_type, count(*) as count
            ORDER BY count DESC
            LIMIT 10
            """
            
            result = session.run(entity_entity_query)
            entity_relationships = list(result)
            
            if entity_relationships:
                print("   ✅ Entity-entity relationships found:")
                for record in entity_relationships:
                    print(f"      {record['relationship_type']}: {record['count']}")
            else:
                print("   ⚠️  No entity-entity relationships found")
            
            # Test 4: Check graph diameter (longest shortest path)
            print("\n🔍 Checking graph diameter...")
            
            diameter_query = """
            MATCH (start), (end)
            WHERE start <> end
            WITH start, end
            MATCH path = shortestPath((start)-[*..10]-(end))
            RETURN length(path) as path_length
            ORDER BY path_length DESC
            LIMIT 1
            """
            
            try:
                result = session.run(diameter_query)
                diameter_record = result.single()
                
                if diameter_record:
                    diameter = diameter_record['path_length']
                    print(f"   📏 Graph diameter: {diameter}")
                    
                    if diameter > 8:
                        print("   ⚠️  Graph diameter is quite large - may indicate poor connectivity")
                    else:
                        print("   ✅ Graph diameter is reasonable")
                else:
                    print("   ⚠️  Could not calculate graph diameter")
                    
            except Exception as e:
                print(f"   ⚠️  Diameter calculation failed: {e}")
            
            # Test 5: Check for disconnected components
            print("\n🔍 Checking for disconnected components...")
            
            components_query = """
            CALL gds.wcc.stream('myGraph') 
            YIELD nodeId, componentId
            RETURN componentId, count(*) as size
            ORDER BY size DESC
            LIMIT 10
            """
            
            try:
                # This requires GDS plugin, so we'll use a simpler approach
                simple_components_query = """
                MATCH (n)
                WITH n
                MATCH path = (n)-[*0..5]-(connected)
                RETURN count(DISTINCT connected) as component_size
                ORDER BY component_size DESC
                LIMIT 5
                """
                
                result = session.run(simple_components_query)
                components = list(result)
                
                if len(components) > 1:
                    print(f"   📊 Found {len(components)} potential components")
                    for i, record in enumerate(components):
                        print(f"      Component {i+1}: ~{record['component_size']} nodes")
                else:
                    print("   ✅ Graph appears to be well connected")
                    
            except Exception as e:
                print(f"   ⚠️  Component analysis failed: {e}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Graph connectivity test failed: {e}")
        return False

def test_data_integrity():
    """Test data integrity and consistency."""
    print("\n🔍 Testing Data Integrity")
    print("=" * 50)
    
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'neo4j')
        password = os.getenv('NEO4J_PASSWORD', 'password')
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            # Test 1: Check for nodes without required properties
            print("\n🔍 Checking for nodes without required properties...")
            
            missing_props_query = """
            MATCH (n)
            WHERE n.name IS NULL OR n.name = ""
            RETURN labels(n) as labels, count(n) as count
            """
            
            result = session.run(missing_props_query)
            missing_props = list(result)
            
            if missing_props:
                print("   ⚠️  Found nodes without name property:")
                for record in missing_props:
                    labels = ':'.join(record['labels']) if record['labels'] else 'No Label'
                    print(f"      {labels}: {record['count']} nodes")
            else:
                print("   ✅ All nodes have name property")
            
            # Test 2: Check for duplicate entities
            print("\n🔍 Checking for duplicate entities...")
            
            duplicates_query = """
            MATCH (n)
            WHERE NOT n:Chunk AND NOT n:File
            WITH n.name as name, labels(n) as labels, count(n) as count
            WHERE count > 1
            RETURN name, labels, count
            ORDER BY count DESC
            LIMIT 10
            """
            
            result = session.run(duplicates_query)
            duplicates = list(result)
            
            if duplicates:
                print("   ⚠️  Found potential duplicate entities:")
                for record in duplicates:
                    labels = ':'.join(record['labels'])
                    print(f"      '{record['name']}' ({labels}): {record['count']} instances")
            else:
                print("   ✅ No obvious duplicate entities found")
            
            # Test 3: Check relationship consistency
            print("\n🔍 Checking relationship consistency...")
            
            relationship_consistency_query = """
            MATCH (a)-[r]->(b)
            WHERE r.confidence_score IS NOT NULL
            WITH type(r) as rel_type, 
                 avg(r.confidence_score) as avg_confidence,
                 min(r.confidence_score) as min_confidence,
                 max(r.confidence_score) as max_confidence,
                 count(r) as count
            RETURN rel_type, avg_confidence, min_confidence, max_confidence, count
            ORDER BY count DESC
            LIMIT 10
            """
            
            result = session.run(relationship_consistency_query)
            rel_stats = list(result)
            
            if rel_stats:
                print("   📊 Relationship confidence statistics:")
                for record in rel_stats:
                    print(f"      {record['rel_type']}: avg={record['avg_confidence']:.3f}, "
                          f"range=[{record['min_confidence']:.3f}, {record['max_confidence']:.3f}], "
                          f"count={record['count']}")
                    
                    if record['min_confidence'] < 0 or record['max_confidence'] > 1:
                        print(f"        ⚠️  Confidence scores outside [0,1] range!")
            else:
                print("   ⚠️  No relationships with confidence scores found")
            
            # Test 4: Check for orphaned chunks
            print("\n🔍 Checking for orphaned chunks...")
            
            orphaned_chunks_query = """
            MATCH (c:Chunk)
            WHERE NOT (c)-[:PART_OF]->(:File)
            RETURN count(c) as orphaned_count
            """
            
            result = session.run(orphaned_chunks_query)
            orphaned_record = result.single()
            
            if orphaned_record and orphaned_record['orphaned_count'] > 0:
                print(f"   ⚠️  Found {orphaned_record['orphaned_count']} orphaned chunks")
            else:
                print("   ✅ All chunks are properly linked to files")
            
            # Test 5: Check entity type consistency
            print("\n🔍 Checking entity type consistency...")
            
            entity_types_query = """
            MATCH (n)
            WHERE NOT n:Chunk AND NOT n:File
            RETURN DISTINCT labels(n) as labels, count(n) as count
            ORDER BY count DESC
            """
            
            result = session.run(entity_types_query)
            entity_types = list(result)
            
            print("   📊 Entity type distribution:")
            for record in entity_types[:10]:  # Show top 10
                labels = ':'.join(record['labels']) if record['labels'] else 'No Label'
                print(f"      {labels}: {record['count']}")
            
            # Check for entities with multiple labels
            multi_label_query = """
            MATCH (n)
            WHERE size(labels(n)) > 1 AND NOT n:Chunk AND NOT n:File
            RETURN labels(n) as labels, count(n) as count
            ORDER BY count DESC
            LIMIT 5
            """
            
            result = session.run(multi_label_query)
            multi_labels = list(result)
            
            if multi_labels:
                print("\n   📊 Entities with multiple labels:")
                for record in multi_labels:
                    labels = ':'.join(record['labels'])
                    print(f"      {labels}: {record['count']}")
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Data integrity test failed: {e}")
        return False

def test_search_result_quality():
    """Test the quality and consistency of search results."""
    print("\n🎯 Testing Search Result Quality")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Get some real chunks for testing
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 5"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:3] if result else []
        
        if not real_chunks:
            print("⚠️  No chunks found for quality testing")
            return True
        
        # Test 1: Consistency across multiple runs
        print("\n🔍 Testing result consistency...")
        
        results = []
        for i in range(3):
            result = search_engine.search(
                chunk_indices=real_chunks,
                query_text="test consistency",
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=20,
                expansion_depth=1
            )
            results.append(result)
        
        # Check if results are consistent
        entity_counts = [len(r.graph_context.entities) for r in results]
        rel_counts = [len(r.graph_context.relationships) for r in results]
        
        if len(set(entity_counts)) == 1 and len(set(rel_counts)) == 1:
            print("   ✅ Results are consistent across runs")
        else:
            print(f"   ⚠️  Results vary: entities={entity_counts}, relationships={rel_counts}")
        
        # Test 2: Score distribution
        print("\n🔍 Testing score distributions...")
        
        result = results[0]  # Use first result
        
        if result.graph_context.entities:
            entity_scores = [e.relevance_score for e in result.graph_context.entities if e.relevance_score is not None]
            if entity_scores:
                avg_entity_score = sum(entity_scores) / len(entity_scores)
                min_entity_score = min(entity_scores)
                max_entity_score = max(entity_scores)
                
                print(f"   📊 Entity scores: avg={avg_entity_score:.3f}, "
                      f"range=[{min_entity_score:.3f}, {max_entity_score:.3f}]")
                
                if min_entity_score < 0 or max_entity_score > 1:
                    print("   ⚠️  Entity scores outside [0,1] range!")
                else:
                    print("   ✅ Entity scores within valid range")
        
        if result.graph_context.relationships:
            rel_scores = [r.confidence_score for r in result.graph_context.relationships if r.confidence_score is not None]
            if rel_scores:
                avg_rel_score = sum(rel_scores) / len(rel_scores)
                min_rel_score = min(rel_scores)
                max_rel_score = max(rel_scores)
                
                print(f"   📊 Relationship scores: avg={avg_rel_score:.3f}, "
                      f"range=[{min_rel_score:.3f}, {max_rel_score:.3f}]")
                
                if min_rel_score < 0 or max_rel_score > 1:
                    print("   ⚠️  Relationship scores outside [0,1] range!")
                else:
                    print("   ✅ Relationship scores within valid range")
        
        # Test 3: Result completeness
        print("\n🔍 Testing result completeness...")
        
        complete_entities = sum(1 for e in result.graph_context.entities 
                              if e.name and e.entity_type and e.relevance_score is not None)
        total_entities = len(result.graph_context.entities)
        
        complete_relationships = sum(1 for r in result.graph_context.relationships 
                                   if r.source_entity and r.target_entity and r.relationship_type)
        total_relationships = len(result.graph_context.relationships)
        
        entity_completeness = complete_entities / max(1, total_entities)
        rel_completeness = complete_relationships / max(1, total_relationships)
        
        print(f"   📊 Entity completeness: {entity_completeness:.1%} ({complete_entities}/{total_entities})")
        print(f"   📊 Relationship completeness: {rel_completeness:.1%} ({complete_relationships}/{total_relationships})")
        
        if entity_completeness >= 0.9 and rel_completeness >= 0.9:
            print("   ✅ High result completeness")
        else:
            print("   ⚠️  Some results may be incomplete")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Search result quality test failed: {e}")
        return False

def main():
    """Main test function for graph connectivity and data integrity."""
    print("🧪 Graph Connectivity and Data Integrity Test Suite")
    print("=" * 70)
    
    test_results = {}
    
    # Test 1: Graph connectivity
    print("\n" + "🔗 PHASE 1: Graph Connectivity Tests" + "\n" + "=" * 50)
    test_results['graph_connectivity'] = test_graph_connectivity()
    
    # Test 2: Data integrity
    print("\n" + "🔍 PHASE 2: Data Integrity Tests" + "\n" + "=" * 50)
    test_results['data_integrity'] = test_data_integrity()
    
    # Test 3: Search result quality
    print("\n" + "🎯 PHASE 3: Search Result Quality Tests" + "\n" + "=" * 50)
    test_results['search_quality'] = test_search_result_quality()
    
    # Final summary
    print("\n" + "📊 CONNECTIVITY & INTEGRITY TEST SUMMARY" + "\n" + "=" * 70)
    
    passed_tests = sum(1 for result in test_results.values() if result is True)
    failed_tests = sum(1 for result in test_results.values() if result is False)
    total_tests = len(test_results)
    
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {failed_tests}/{total_tests}")
    
    print(f"\n📋 Detailed Results:")
    for test_name, result in test_results.items():
        status = "✅ PASS" if result is True else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    if failed_tests == 0:
        print(f"\n🎉 ALL CONNECTIVITY & INTEGRITY TESTS PASSED!")
    else:
        print(f"\n⚠️  {failed_tests} test(s) failed. Review graph structure and data quality.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
