"""
Integration Test Suite

This script tests the integration between your existing Pinecone setup
and the new Enterprise Knowledge Graph system.
"""

import os
import sys
import json
import unittest
import logging
from typing import Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enterprise_kg_processor import EnterpriseKGProcessor
from hybrid_search_engine import HybridSearchEngine
from integration_example import YourExistingPineconeSystem, IntegratedEnterpriseSystem

# Configure logging for tests
logging.basicConfig(level=logging.WARNING)  # Reduce noise during tests


class TestEnterpriseKGProcessor(unittest.TestCase):
    """Test the Enterprise KG Processor."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test configuration."""
        cls.neo4j_config = {
            "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            "user": os.getenv("NEO4J_USER", "neo4j"),
            "password": os.getenv("NEO4J_PASSWORD", "password")
        }
        
        cls.llm_config = {
            "provider": "openai",
            "model": "gpt-4o"
        }
    
    def setUp(self):
        """Set up each test."""
        try:
            self.kg_processor = EnterpriseKGProcessor(
                neo4j_config=self.neo4j_config,
                llm_config=self.llm_config
            )
        except Exception as e:
            self.skipTest(f"Could not initialize KG processor: {e}")
    
    def test_processor_initialization(self):
        """Test that the processor initializes correctly."""
        self.assertIsNotNone(self.kg_processor)
        self.assertIsNotNone(self.kg_processor.processor)
    
    def test_connection_tests(self):
        """Test connection testing functionality."""
        connections = self.kg_processor.test_connections()
        
        self.assertIsInstance(connections, dict)
        self.assertIn("neo4j", connections)
        self.assertIn("llm_api", connections)
        self.assertIsInstance(connections["neo4j"], bool)
        self.assertIsInstance(connections["llm_api"], bool)
    
    def test_get_processing_stats(self):
        """Test getting processing statistics."""
        stats = self.kg_processor.get_processing_stats()
        
        if "error" not in stats:
            self.assertIn("entity_count", stats)
            self.assertIn("relationship_count", stats)
            self.assertIn("timestamp", stats)
    
    def tearDown(self):
        """Clean up after each test."""
        if hasattr(self, 'kg_processor'):
            self.kg_processor.close()


class TestHybridSearchEngine(unittest.TestCase):
    """Test the Hybrid Search Engine."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test configuration."""
        cls.pinecone_config = {
            "api_key": "mock-api-key",
            "environment": "test-env",
            "index_name": "test-index"
        }
        
        cls.neo4j_config = {
            "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            "user": os.getenv("NEO4J_USER", "neo4j"),
            "password": os.getenv("NEO4J_PASSWORD", "password")
        }
        
        cls.llm_config = {
            "provider": "openai",
            "model": "gpt-4o"
        }
    
    def setUp(self):
        """Set up each test."""
        try:
            # Create mock Pinecone system
            self.mock_pinecone = YourExistingPineconeSystem(
                api_key=self.pinecone_config["api_key"],
                environment=self.pinecone_config["environment"],
                index_name=self.pinecone_config["index_name"]
            )
            
            # Create KG processor
            self.kg_processor = EnterpriseKGProcessor(
                neo4j_config=self.neo4j_config,
                llm_config=self.llm_config
            )
            
            # Create hybrid search engine
            self.hybrid_search = HybridSearchEngine(
                existing_pinecone_client=self.mock_pinecone,
                kg_processor=self.kg_processor
            )
            
        except Exception as e:
            self.skipTest(f"Could not initialize hybrid search: {e}")
    
    def test_hybrid_search_initialization(self):
        """Test that the hybrid search engine initializes correctly."""
        self.assertIsNotNone(self.hybrid_search)
        self.assertIsNotNone(self.hybrid_search.pinecone_client)
        self.assertIsNotNone(self.hybrid_search.kg_processor)
    
    def test_pinecone_only_search(self):
        """Test Pinecone-only search."""
        results = self.hybrid_search.search(
            query="Who is working on Project Alpha?",
            org_id="test_org",
            method="pinecone"
        )
        
        self.assertEqual(results["method"], "pinecone_only")
        self.assertIn("semantic_results", results)
        self.assertIn("source_files", results)
    
    def test_neo4j_only_search(self):
        """Test Neo4j-only search."""
        results = self.hybrid_search.search(
            query="Who is working on Project Alpha?",
            method="neo4j"
        )
        
        self.assertEqual(results["method"], "neo4j_only")
        self.assertIn("extracted_entities", results)
        self.assertIn("structured_relationships", results)
    
    def test_hybrid_search(self):
        """Test hybrid search combining both systems."""
        results = self.hybrid_search.search(
            query="Who is working on Project Alpha?",
            org_id="test_org",
            method="hybrid"
        )
        
        self.assertEqual(results["method"], "hybrid")
        self.assertIn("semantic_search", results)
        self.assertIn("knowledge_graph", results)
        self.assertIn("answer", results)
        self.assertIn("confidence", results)
    
    def test_search_stats(self):
        """Test getting search statistics."""
        stats = self.hybrid_search.get_search_stats()
        
        self.assertIn("knowledge_graph", stats)
        self.assertIn("search_engine", stats)
    
    def tearDown(self):
        """Clean up after each test."""
        if hasattr(self, 'kg_processor'):
            self.kg_processor.close()


class TestIntegratedSystem(unittest.TestCase):
    """Test the complete integrated system."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test configuration."""
        cls.pinecone_config = {
            "api_key": "mock-api-key",
            "environment": "test-env",
            "index_name": "test-index"
        }
        
        cls.neo4j_config = {
            "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            "user": os.getenv("NEO4J_USER", "neo4j"),
            "password": os.getenv("NEO4J_PASSWORD", "password")
        }
        
        cls.llm_config = {
            "provider": "openai",
            "model": "gpt-4o"
        }
    
    def setUp(self):
        """Set up each test."""
        try:
            self.integrated_system = IntegratedEnterpriseSystem(
                pinecone_config=self.pinecone_config,
                neo4j_config=self.neo4j_config,
                llm_config=self.llm_config
            )
        except Exception as e:
            self.skipTest(f"Could not initialize integrated system: {e}")
    
    def test_system_initialization(self):
        """Test that the integrated system initializes correctly."""
        self.assertIsNotNone(self.integrated_system)
        self.assertIsNotNone(self.integrated_system.pinecone_system)
        self.assertIsNotNone(self.integrated_system.kg_processor)
        self.assertIsNotNone(self.integrated_system.hybrid_search)
    
    def test_connection_tests(self):
        """Test all system connections."""
        connections = self.integrated_system.test_all_connections()
        
        self.assertIsInstance(connections, dict)
        self.assertIn("pinecone", connections)
        self.assertIn("neo4j", connections)
        self.assertIn("llm_api", connections)
    
    def test_enhanced_search(self):
        """Test enhanced search functionality."""
        results = self.integrated_system.enhanced_search(
            query="Who is working on Project Alpha?",
            org_id="test_org"
        )
        
        self.assertIn("query", results)
        self.assertIn("hybrid_search", results)
        self.assertIn("existing_pinecone", results)
        self.assertIn("enhancement_summary", results)
    
    def test_system_stats(self):
        """Test getting system statistics."""
        stats = self.integrated_system.get_system_stats()
        
        self.assertIn("pinecone_stats", stats)
        self.assertIn("kg_stats", stats)
        self.assertIn("hybrid_search_stats", stats)


class TestDocumentProcessing(unittest.TestCase):
    """Test document processing functionality."""
    
    def setUp(self):
        """Set up test documents."""
        self.test_doc_content = """
        # Project Alpha Status Report
        
        Sarah Johnson is managing Project Alpha, which involves developing a new CRM System.
        The project team includes Mike Chen as the lead developer and Lisa Rodriguez as the UX designer.
        
        The Engineering Department, led by Jennifer Walsh, is collaborating on this initiative.
        Mike Chen reports that the CRM integration with the ERP Platform is on track.
        """
        
        # Create temporary test document
        os.makedirs("test_documents", exist_ok=True)
        with open("test_documents/test_project_report.md", "w") as f:
            f.write(self.test_doc_content)
    
    def test_document_processing_with_mock_data(self):
        """Test document processing with mock data."""
        # This test uses mock data to avoid requiring actual API connections
        
        # Mock processing results
        mock_results = {
            "document_id": "test_project_report.md",
            "entities_extracted": ["Sarah Johnson", "Project Alpha", "Mike Chen", "CRM System"],
            "relationships_extracted": [
                {"subject": "Sarah Johnson", "predicate": "manages", "object": "Project Alpha"},
                {"subject": "Mike Chen", "predicate": "involved_in", "object": "Project Alpha"}
            ]
        }
        
        # Verify mock data structure
        self.assertIn("document_id", mock_results)
        self.assertIn("entities_extracted", mock_results)
        self.assertIn("relationships_extracted", mock_results)
        self.assertGreater(len(mock_results["entities_extracted"]), 0)
        self.assertGreater(len(mock_results["relationships_extracted"]), 0)
    
    def tearDown(self):
        """Clean up test documents."""
        import shutil
        if os.path.exists("test_documents"):
            shutil.rmtree("test_documents")


def run_integration_tests():
    """Run all integration tests."""
    print("🧪 Running Enterprise KG Integration Tests")
    print("=" * 50)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestEnterpriseKGProcessor,
        TestHybridSearchEngine,
        TestIntegratedSystem,
        TestDocumentProcessing
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if result.skipped:
        print("\nSkipped:")
        for test, reason in result.skipped:
            print(f"- {test}: {reason}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    
    if success:
        print("\n🎉 All tests passed! Integration is working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the errors above.")
    
    print("\nNext steps:")
    print("1. Fix any failing tests")
    print("2. Configure your actual API credentials")
    print("3. Test with real documents")
    print("4. Deploy to your production environment")
    
    return success


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
