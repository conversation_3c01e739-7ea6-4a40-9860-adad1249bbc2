#!/usr/bin/env python3
"""
Test Hybrid Search Engine with Real Neo4j Data

This script tests the hybrid search engine with actual data in Neo4j,
checking traversal and scoring functionality.
"""

import os
import sys
from typing import List, Set
from dotenv import load_dotenv

# Add the enterprise_kg_minimal to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'enterprise_kg_minimal'))

load_dotenv()

def check_neo4j_data():
    """Check what data exists in Neo4j for testing."""
    print("🔍 Checking Neo4j Database Content")
    print("=" * 50)
    
    try:
        from neo4j import GraphDatabase
        
        uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
        user = os.getenv('NEO4J_USER', 'neo4j')
        password = os.getenv('NEO4J_PASSWORD', 'password')
        
        print(f"Connecting to: {uri}")
        
        driver = GraphDatabase.driver(uri, auth=(user, password))
        
        with driver.session() as session:
            # Check node counts by label
            result = session.run("MATCH (n) RETURN labels(n) as labels, count(n) as count")
            print("\n📊 Node Counts by Label:")
            node_data = {}
            for record in result:
                labels = record['labels']
                count = record['count']
                label_str = ':'.join(labels) if labels else 'No Label'
                node_data[label_str] = count
                print(f"   {label_str}: {count}")
            
            # Check relationship counts
            result = session.run("MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count")
            print("\n🔗 Relationship Counts by Type:")
            rel_data = {}
            for record in result:
                rel_type = record['rel_type']
                count = record['count']
                rel_data[rel_type] = count
                print(f"   {rel_type}: {count}")
            
            # Get sample entities for testing
            print("\n🎯 Sample Entities for Testing:")
            
            # Get some Person entities
            result = session.run("MATCH (p:Person) RETURN p.name LIMIT 5")
            persons = [record['p.name'] for record in result]
            if persons:
                print(f"   Persons: {persons}")
            
            # Get some Company entities
            result = session.run("MATCH (c:Company) RETURN c.name LIMIT 5")
            companies = [record['c.name'] for record in result]
            if companies:
                print(f"   Companies: {companies}")
            
            # Get some Project entities
            result = session.run("MATCH (p:Project) RETURN p.name LIMIT 5")
            projects = [record['p.name'] for record in result]
            if projects:
                print(f"   Projects: {projects}")
            
            # Get some chunks for testing
            result = session.run("MATCH (c:Chunk) RETURN c.id LIMIT 5")
            chunks = [record['c.id'] for record in result]
            if chunks:
                print(f"   Sample Chunk IDs: {chunks}")
            
            driver.close()
            
            return {
                'nodes': node_data,
                'relationships': rel_data,
                'sample_entities': {
                    'persons': persons,
                    'companies': companies,
                    'projects': projects
                },
                'sample_chunks': chunks
            }
            
    except Exception as e:
        print(f"❌ Error checking Neo4j data: {e}")
        return None

def test_hybrid_search_initialization():
    """Test hybrid search engine initialization."""
    print("\n🚀 Testing Hybrid Search Engine Initialization")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Test getting available types
        available_types = search_engine.get_available_types()
        
        print("✅ Search engine initialized successfully")
        print(f"   Entity types available: {available_types['total_entity_types']}")
        print(f"   Relationship types available: {available_types['total_relationship_types']}")
        print(f"   Entity categories: {len(available_types['entity_categories'])}")
        
        # Show some available types
        print(f"\n📋 Available Entity Types (first 10):")
        for i, entity_type in enumerate(available_types['entity_types'][:10]):
            print(f"   {i+1}. {entity_type}")
        
        print(f"\n📋 Available Relationship Types (first 10):")
        for i, rel_type in enumerate(available_types['relationship_types'][:10]):
            print(f"   {i+1}. {rel_type}")
        
        search_engine.neo4j_client.close()
        return search_engine, available_types
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return None, None

def test_entity_neighborhood_search(sample_entities):
    """Test entity neighborhood search with real entities."""
    print("\n🌐 Testing Entity Neighborhood Search")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Test with different entity types
        test_entities = []
        if sample_entities['persons']:
            test_entities.append(('Person', sample_entities['persons'][0]))
        if sample_entities['companies']:
            test_entities.append(('Company', sample_entities['companies'][0]))
        if sample_entities['projects']:
            test_entities.append(('Project', sample_entities['projects'][0]))
        
        if not test_entities:
            print("⚠️  No sample entities found for testing")
            return
        
        for entity_type, entity_name in test_entities:
            print(f"\n🔍 Testing neighborhood for {entity_type}: '{entity_name}'")
            
            result = search_engine.search_entity_neighborhood(
                entity_name=entity_name,
                max_depth=2,
                max_results=20
            )
            
            print(f"   ✅ Search completed in {result.processing_time_ms:.2f}ms")
            print(f"   📊 Results: {result.total_results} total")
            print(f"   🏷️  Entities found: {len(result.graph_context.entities)}")
            print(f"   🔗 Relationships found: {len(result.graph_context.relationships)}")
            print(f"   📈 Coverage score: {result.coverage_score:.3f}")
            print(f"   🎯 Coherence score: {result.coherence_score:.3f}")
            print(f"   ⭐ Relevance score: {result.relevance_score:.3f}")
            
            # Show connected entities
            if result.graph_context.entities:
                print(f"   🔗 Connected entities (first 5):")
                for i, entity in enumerate(result.graph_context.entities[:5]):
                    print(f"      {i+1}. {entity.name} ({entity.entity_type})")
            
            # Show relationships
            if result.graph_context.relationships:
                print(f"   🔗 Relationships (first 3):")
                for i, rel in enumerate(result.graph_context.relationships[:3]):
                    print(f"      {i+1}. {rel.source_entity} -[{rel.relationship_type}]-> {rel.target_entity}")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Entity neighborhood search failed: {e}")
        return False

def test_chunk_based_search(sample_chunks):
    """Test hybrid search with chunk indices."""
    print("\n📄 Testing Chunk-Based Hybrid Search")
    print("=" * 50)

    if not sample_chunks:
        print("⚠️  No sample chunks found for testing")
        return False

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # Test with different strategies
        strategies = [
            SearchStrategy.CHUNK_EXPANSION,
            SearchStrategy.ENTITY_CENTRIC,
            SearchStrategy.HYBRID
        ]

        test_chunks = sample_chunks[:3]  # Use first 3 chunks

        for strategy in strategies:
            print(f"\n🔍 Testing {strategy.value} strategy with chunks: {test_chunks}")

            try:
                result = search_engine.search(
                    chunk_indices=test_chunks,
                    query_text="test query for hybrid search",
                    strategy=strategy,
                    max_results=30,
                    expansion_depth=2
                )

                print(f"   ✅ Search completed in {result.processing_time_ms:.2f}ms")
                print(f"   📊 Total results: {result.total_results}")
                print(f"   🏷️  Entities: {len(result.graph_context.entities)}")
                print(f"   🔗 Relationships: {len(result.graph_context.relationships)}")
                print(f"   📈 Coverage score: {result.coverage_score:.3f}")
                print(f"   🎯 Coherence score: {result.coherence_score:.3f}")
                print(f"   ⭐ Relevance score: {result.relevance_score:.3f}")

                # Show debug info if there are errors
                if result.debug_info and 'error' in result.debug_info:
                    print(f"   ⚠️  Debug info: {result.debug_info}")

                # Show some results
                if result.graph_context.entities:
                    print(f"   🔗 Sample entities (first 3):")
                    for i, entity in enumerate(result.graph_context.entities[:3]):
                        print(f"      {i+1}. {entity.name} ({entity.entity_type})")

                if result.graph_context.relationships:
                    print(f"   🔗 Sample relationships (first 3):")
                    for i, rel in enumerate(result.graph_context.relationships[:3]):
                        print(f"      {i+1}. {rel.source_entity} -[{rel.relationship_type}]-> {rel.target_entity}")

            except Exception as strategy_error:
                print(f"   ❌ Strategy {strategy.value} failed: {strategy_error}")
                import traceback
                traceback.print_exc()

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Chunk-based search failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_graph_traversal_and_scoring():
    """Test graph traversal and scoring functionality specifically."""
    print("\n🎯 Testing Graph Traversal and Scoring")
    print("=" * 50)

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # First, let's check what chunks exist and pick some real ones
        print("🔍 Finding real chunks in the database...")

        query = "MATCH (c:Chunk) RETURN c.id LIMIT 10"
        result = search_engine.neo4j_client.execute_query(query)

        if not result:
            print("⚠️  No chunks found in database")
            search_engine.neo4j_client.close()
            return False

        real_chunks = [record['c.id'] for record in result]
        print(f"   Found {len(real_chunks)} chunks: {real_chunks[:3]}...")

        # Test with a small set of real chunks
        test_chunks = real_chunks[:2]

        print(f"\n🧪 Testing traversal with chunks: {test_chunks}")

        # Test chunk expansion strategy specifically
        result = search_engine.search(
            chunk_indices=test_chunks,
            query_text="test traversal and scoring",
            strategy=SearchStrategy.CHUNK_EXPANSION,
            max_results=20,
            expansion_depth=1,  # Start with depth 1
            include_chunk_context=True,
            include_file_context=True
        )

        print(f"✅ Traversal completed in {result.processing_time_ms:.2f}ms")
        print(f"📊 Results found:")
        print(f"   - Total results: {result.total_results}")
        print(f"   - Entities: {len(result.graph_context.entities)}")
        print(f"   - Relationships: {len(result.graph_context.relationships)}")
        print(f"   - Source chunks: {len(result.graph_context.source_chunks)}")

        print(f"📈 Scoring metrics:")
        print(f"   - Coverage score: {result.coverage_score:.3f}")
        print(f"   - Coherence score: {result.coherence_score:.3f}")
        print(f"   - Relevance score: {result.relevance_score:.3f}")

        # Show detailed results if found
        if result.graph_context.entities:
            print(f"\n🏷️  Entities found:")
            for i, entity in enumerate(result.graph_context.entities[:5]):
                print(f"   {i+1}. {entity.name} ({entity.entity_type}) - Score: {entity.relevance_score:.3f}")

        if result.graph_context.relationships:
            print(f"\n🔗 Relationships found:")
            for i, rel in enumerate(result.graph_context.relationships[:5]):
                print(f"   {i+1}. {rel.source_entity} -[{rel.relationship_type}]-> {rel.target_entity}")
                print(f"      Confidence: {rel.confidence_score:.3f}, Relevance: {rel.relevance_score:.3f}")

        # Test with deeper traversal
        if result.total_results > 0:
            print(f"\n🔄 Testing deeper traversal (depth=2)...")

            deeper_result = search_engine.search(
                chunk_indices=test_chunks,
                query_text="test deeper traversal",
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=30,
                expansion_depth=2,
                include_chunk_context=True
            )

            print(f"   Deeper traversal results: {deeper_result.total_results} (vs {result.total_results} at depth 1)")
            print(f"   Entities: {len(deeper_result.graph_context.entities)} (vs {len(result.graph_context.entities)})")
            print(f"   Relationships: {len(deeper_result.graph_context.relationships)} (vs {len(result.graph_context.relationships)})")

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Graph traversal test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_query_aware_entity_scoring():
    """Test the new query-aware entity scoring functionality."""
    print("\n🔍 Testing Query-Aware Entity Scoring")
    print("=" * 50)

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # Get some real chunks
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 5"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:2]

        # Test different query types to see how scoring changes
        test_queries = [
            "machine learning project development",
            "team collaboration and management",
            "artificial intelligence platform",
            "software engineering tools",
            "business process optimization"
        ]

        for query_text in test_queries:
            print(f"\n🔍 Testing query: '{query_text}'")

            result = search_engine.search(
                chunk_indices=real_chunks,
                query_text=query_text,
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=15,
                expansion_depth=1
            )

            print(f"   Results: {result.total_results} entities found")

            if result.graph_context.entities:
                # Show top 3 entities with their relevance scores
                top_entities = sorted(result.graph_context.entities,
                                    key=lambda e: e.relevance_score, reverse=True)[:3]

                print(f"   Top entities by query relevance:")
                for i, entity in enumerate(top_entities):
                    print(f"      {i+1}. {entity.name} ({entity.entity_type})")
                    print(f"         Relevance: {entity.relevance_score:.3f}")
                    print(f"         Match reason: {entity.match_reason}")

        # Compare query-aware vs non-query-aware scoring
        print(f"\n🔄 Comparing Query-Aware vs Traditional Scoring")

        test_query = "machine learning project"

        # Query-aware search
        query_aware_result = search_engine.search(
            chunk_indices=real_chunks,
            query_text=test_query,
            strategy=SearchStrategy.CHUNK_EXPANSION,
            max_results=10,
            expansion_depth=1
        )

        # Traditional search (without query text)
        traditional_result = search_engine.search(
            chunk_indices=real_chunks,
            query_text=None,  # No query text
            strategy=SearchStrategy.CHUNK_EXPANSION,
            max_results=10,
            expansion_depth=1
        )

        print(f"   Query-aware results: {len(query_aware_result.graph_context.entities)} entities")
        print(f"   Traditional results: {len(traditional_result.graph_context.entities)} entities")

        if query_aware_result.graph_context.entities and traditional_result.graph_context.entities:
            print(f"\n   Top entity comparison:")

            qa_top = sorted(query_aware_result.graph_context.entities,
                          key=lambda e: e.relevance_score, reverse=True)[0]
            trad_top = sorted(traditional_result.graph_context.entities,
                            key=lambda e: e.relevance_score, reverse=True)[0]

            print(f"   Query-aware top: {qa_top.name} (score: {qa_top.relevance_score:.3f})")
            print(f"   Traditional top: {trad_top.name} (score: {trad_top.relevance_score:.3f})")

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Query-aware scoring test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases_empty_inputs():
    """Test hybrid search with empty and invalid inputs."""
    print("\n🚫 Testing Edge Cases - Empty/Invalid Inputs")
    print("=" * 50)

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        edge_cases = [
            ("Empty chunk list", []),
            ("None chunk list", None),
            ("Invalid chunk IDs", ["nonexistent_chunk_1", "invalid_chunk_2"]),
            ("Mixed valid/invalid chunks", ["valid_chunk", "invalid_chunk"]),
            ("Very long chunk list", [f"chunk_{i}" for i in range(100)]),
            ("Chunks with special characters", ["chunk@#$%", "chunk with spaces", "chunk/with/slashes"]),
            ("Empty string chunks", ["", " ", "   "]),
        ]

        for test_name, chunk_input in edge_cases:
            print(f"\n🧪 Testing: {test_name}")

            try:
                if chunk_input is None:
                    # Skip None test as it would cause TypeError
                    print("   ⚠️  Skipped - None input would cause TypeError")
                    continue

                result = search_engine.search(
                    chunk_indices=chunk_input,
                    query_text="test query",
                    strategy=SearchStrategy.CHUNK_EXPANSION,
                    max_results=10,
                    expansion_depth=1
                )

                print(f"   ✅ Handled gracefully - Results: {result.total_results}")
                print(f"   📊 Processing time: {result.processing_time_ms:.2f}ms")

                if result.debug_info and 'error' in result.debug_info:
                    print(f"   ⚠️  Debug info: {result.debug_info}")

            except Exception as e:
                print(f"   ❌ Failed with error: {e}")

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Edge case testing failed: {e}")
        return False

def test_edge_cases_extreme_parameters():
    """Test hybrid search with extreme parameter values."""
    print("\n⚡ Testing Edge Cases - Extreme Parameters")
    print("=" * 50)

    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # Get some real chunks for testing
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 3"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:2] if result else ["test_chunk"]

        extreme_cases = [
            ("Zero max_results", {"max_results": 0}),
            ("Negative max_results", {"max_results": -5}),
            ("Very large max_results", {"max_results": 10000}),
            ("Zero expansion_depth", {"expansion_depth": 0}),
            ("Negative expansion_depth", {"expansion_depth": -1}),
            ("Very large expansion_depth", {"expansion_depth": 10}),
            ("Very low confidence threshold", {"min_confidence_score": -1.0}),
            ("Very high confidence threshold", {"min_confidence_score": 2.0}),
            ("Empty query text", {"query_text": ""}),
            ("Very long query text", {"query_text": "test " * 1000}),
            ("Query with special characters", {"query_text": "!@#$%^&*()_+{}|:<>?[]\\;'\",./"})
        ]

        for test_name, params in extreme_cases:
            print(f"\n🧪 Testing: {test_name}")

            try:
                default_params = {
                    "chunk_indices": real_chunks,
                    "strategy": SearchStrategy.CHUNK_EXPANSION,
                    "max_results": 10,
                    "expansion_depth": 1,
                    "query_text": "test query",
                    "min_confidence_score": 0.3
                }

                # Override with test parameters
                test_params = {**default_params, **params}

                result = search_engine.search(**test_params)

                print(f"   ✅ Handled gracefully - Results: {result.total_results}")
                print(f"   📊 Processing time: {result.processing_time_ms:.2f}ms")

                if result.debug_info and 'error' in result.debug_info:
                    print(f"   ⚠️  Debug info: {result.debug_info}")

            except Exception as e:
                print(f"   ❌ Failed with error: {e}")

        search_engine.neo4j_client.close()
        return True

    except Exception as e:
        print(f"❌ Extreme parameter testing failed: {e}")
        return False

def test_concurrent_search_operations():
    """Test concurrent hybrid search operations."""
    print("\n🔄 Testing Concurrent Search Operations")
    print("=" * 50)

    try:
        import threading
        import time
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        # Create multiple search engines for concurrent testing
        search_engines = []
        for i in range(3):
            engine = create_hybrid_search_engine(
                neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
                neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
                neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
            )
            search_engines.append(engine)

        # Get some real chunks
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 5"
        result = search_engines[0].neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:3] if result else ["test_chunk"]

        results = []
        errors = []

        def concurrent_search(engine_id, search_engine):
            """Perform search in a separate thread."""
            try:
                start_time = time.time()

                result = search_engine.search(
                    chunk_indices=real_chunks,
                    query_text=f"concurrent test query {engine_id}",
                    strategy=SearchStrategy.CHUNK_EXPANSION,
                    max_results=15,
                    expansion_depth=1
                )

                end_time = time.time()

                results.append({
                    'engine_id': engine_id,
                    'total_results': result.total_results,
                    'processing_time_ms': result.processing_time_ms,
                    'thread_time': (end_time - start_time) * 1000,
                    'entities': len(result.graph_context.entities),
                    'relationships': len(result.graph_context.relationships)
                })

            except Exception as e:
                errors.append(f"Engine {engine_id}: {e}")

        # Start concurrent searches
        threads = []
        for i, engine in enumerate(search_engines):
            thread = threading.Thread(target=concurrent_search, args=(i, engine))
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # Analyze results
        print(f"\n📊 Concurrent Search Results:")
        print(f"   Successful searches: {len(results)}")
        print(f"   Failed searches: {len(errors)}")

        if results:
            avg_processing_time = sum(r['processing_time_ms'] for r in results) / len(results)
            avg_thread_time = sum(r['thread_time'] for r in results) / len(results)

            print(f"   Average processing time: {avg_processing_time:.2f}ms")
            print(f"   Average thread time: {avg_thread_time:.2f}ms")

            for result in results:
                print(f"   Engine {result['engine_id']}: {result['total_results']} results "
                      f"({result['entities']} entities, {result['relationships']} relationships)")

        if errors:
            print(f"\n❌ Errors encountered:")
            for error in errors:
                print(f"   {error}")

        # Clean up
        for engine in search_engines:
            engine.neo4j_client.close()

        return len(errors) == 0

    except Exception as e:
        print(f"❌ Concurrent testing failed: {e}")
        return False

def test_memory_and_performance():
    """Test memory usage and performance under load."""
    print("\n🚀 Testing Memory Usage and Performance")
    print("=" * 50)

    try:
        import psutil
        import time
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy

        # Monitor memory usage
        process = psutil.Process()
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        print(f"📊 Initial memory usage: {initial_memory:.2f} MB")

        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )

        # Get real chunks
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 10"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:5] if result else ["test_chunk"]

        # Performance test scenarios
        test_scenarios = [
            ("Small search", {"max_results": 5, "expansion_depth": 1}),
            ("Medium search", {"max_results": 20, "expansion_depth": 2}),
            ("Large search", {"max_results": 50, "expansion_depth": 2}),
            ("Deep search", {"max_results": 30, "expansion_depth": 3}),
        ]

        performance_results = []

        for scenario_name, params in test_scenarios:
            print(f"\n🧪 Testing: {scenario_name}")

            # Measure memory before
            memory_before = process.memory_info().rss / 1024 / 1024

            # Run multiple iterations to get average
            times = []
            for i in range(3):
                start_time = time.time()

                result = search_engine.search(
                    chunk_indices=real_chunks,
                    query_text=f"performance test {scenario_name} iteration {i}",
                    strategy=SearchStrategy.HYBRID,
                    **params
                )

                end_time = time.time()
                times.append((end_time - start_time) * 1000)

            # Measure memory after
            memory_after = process.memory_info().rss / 1024 / 1024

            avg_time = sum(times) / len(times)
            memory_delta = memory_after - memory_before

            performance_results.append({
                'scenario': scenario_name,
                'avg_time_ms': avg_time,
                'memory_delta_mb': memory_delta,
                'results_count': result.total_results,
                'params': params
            })

            print(f"   ⏱️  Average time: {avg_time:.2f}ms")
            print(f"   💾 Memory delta: {memory_delta:.2f}MB")
            print(f"   📊 Results: {result.total_results}")

        # Memory leak test - run same search multiple times
        print(f"\n🔍 Memory Leak Test (10 iterations)")

        memory_readings = []
        for i in range(10):
            search_engine.search(
                chunk_indices=real_chunks[:2],
                query_text=f"memory leak test {i}",
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=10,
                expansion_depth=1
            )

            current_memory = process.memory_info().rss / 1024 / 1024
            memory_readings.append(current_memory)

        memory_trend = memory_readings[-1] - memory_readings[0]
        print(f"   📈 Memory trend over 10 iterations: {memory_trend:.2f}MB")

        if memory_trend > 50:  # More than 50MB increase
            print(f"   ⚠️  Potential memory leak detected!")
        else:
            print(f"   ✅ Memory usage appears stable")

        # Performance summary
        print(f"\n📊 Performance Summary:")
        for result in performance_results:
            print(f"   {result['scenario']}: {result['avg_time_ms']:.2f}ms "
                  f"({result['results_count']} results, {result['memory_delta_mb']:.2f}MB)")

        search_engine.neo4j_client.close()

        final_memory = process.memory_info().rss / 1024 / 1024
        total_memory_delta = final_memory - initial_memory
        print(f"\n💾 Total memory delta: {total_memory_delta:.2f}MB")

        return True

    except ImportError:
        print("⚠️  psutil not available - skipping memory tests")
        return True
    except Exception as e:
        print(f"❌ Performance testing failed: {e}")
        return False

def main():
    """Main test function with comprehensive edge case coverage."""
    print("🧪 Comprehensive Hybrid Search Engine Test Suite")
    print("=" * 70)

    test_results = {}

    # Step 1: Check Neo4j data
    print("\n" + "🔍 PHASE 1: Data Validation" + "\n" + "=" * 50)
    neo4j_data = check_neo4j_data()
    if not neo4j_data:
        print("❌ Cannot proceed without Neo4j data")
        return
    test_results['data_validation'] = True

    # Step 2: Test initialization
    print("\n" + "🚀 PHASE 2: Initialization Tests" + "\n" + "=" * 50)
    search_engine, available_types = test_hybrid_search_initialization()
    if not search_engine:
        print("❌ Cannot proceed without search engine")
        return
    test_results['initialization'] = True

    # Step 3: Basic functionality tests
    print("\n" + "🔧 PHASE 3: Basic Functionality Tests" + "\n" + "=" * 50)

    if any(neo4j_data['sample_entities'].values()):
        test_results['entity_neighborhood'] = test_entity_neighborhood_search(neo4j_data['sample_entities'])
    else:
        print("⚠️  Skipping entity neighborhood test - no sample entities found")
        test_results['entity_neighborhood'] = None

    if neo4j_data['sample_chunks']:
        test_results['chunk_based_search'] = test_chunk_based_search(neo4j_data['sample_chunks'])
    else:
        print("⚠️  Skipping chunk-based test - no sample chunks found")
        test_results['chunk_based_search'] = None

    # Step 4: Advanced functionality tests
    print("\n" + "🎯 PHASE 4: Advanced Functionality Tests" + "\n" + "=" * 50)
    test_results['graph_traversal'] = test_graph_traversal_and_scoring()
    test_results['query_aware_scoring'] = test_query_aware_entity_scoring()

    # Step 5: Edge case tests
    print("\n" + "🚫 PHASE 5: Edge Case Tests" + "\n" + "=" * 50)
    test_results['empty_inputs'] = test_edge_cases_empty_inputs()
    test_results['extreme_parameters'] = test_edge_cases_extreme_parameters()

    # Step 6: Stress and performance tests
    print("\n" + "⚡ PHASE 6: Stress and Performance Tests" + "\n" + "=" * 50)
    test_results['concurrent_operations'] = test_concurrent_search_operations()
    test_results['memory_performance'] = test_memory_and_performance()

    # Final summary
    print("\n" + "📊 FINAL TEST SUMMARY" + "\n" + "=" * 70)

    passed_tests = sum(1 for result in test_results.values() if result is True)
    failed_tests = sum(1 for result in test_results.values() if result is False)
    skipped_tests = sum(1 for result in test_results.values() if result is None)
    total_tests = len(test_results)

    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {failed_tests}/{total_tests}")
    print(f"⚠️  Skipped: {skipped_tests}/{total_tests}")

    print(f"\n📋 Detailed Results:")
    for test_name, result in test_results.items():
        status = "✅ PASS" if result is True else "❌ FAIL" if result is False else "⚠️  SKIP"
        print(f"   {test_name}: {status}")

    if failed_tests == 0:
        print(f"\n🎉 ALL TESTS PASSED! Hybrid search is ready for production.")
    else:
        print(f"\n⚠️  {failed_tests} test(s) failed. Review issues before deployment.")

    print("=" * 70)

if __name__ == "__main__":
    main()
