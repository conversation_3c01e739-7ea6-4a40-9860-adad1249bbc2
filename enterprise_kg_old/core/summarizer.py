"""
Document summarization component for Enterprise KG

This module handles document summarization using LLMs,
following the CocoIndex pattern for LLM-based extraction.
"""

import cocoindex
from typing import Optional
from ..constants.schemas import DocumentSummary


class DocumentSummarizer:
    """
    Handles document summarization using LLM extraction.
    
    This class provides document summarization capabilities
    similar to CocoIndex's ExtractByLlm function.
    """
    
    def __init__(
        self,
        llm_spec: cocoindex.LlmSpec,
        custom_instruction: Optional[str] = None
    ):
        """
        Initialize the document summarizer.
        
        Args:
            llm_spec: LLM specification (OpenAI, Anthropic, etc.)
            custom_instruction: Optional custom instruction for summarization
        """
        self.llm_spec = llm_spec
        self.custom_instruction = custom_instruction or self._get_default_instruction()
    
    def _get_default_instruction(self) -> str:
        """Get the default instruction for document summarization."""
        return (
            "Please analyze the document and provide a comprehensive summary. "
            "Focus on extracting the main title, key points, document type, "
            "and important topics discussed. Be concise but thorough. "
            "If the document type is unclear, make your best assessment based on content and structure."
        )
    
    def create_extraction_function(self) -> cocoindex.functions.ExtractByLlm:
        """
        Create a CocoIndex ExtractByLlm function for document summarization.
        
        Returns:
            Configured ExtractByLlm function for summarization
        """
        return cocoindex.functions.ExtractByLlm(
            llm_spec=self.llm_spec,
            output_type=DocumentSummary,
            instruction=self.custom_instruction
        )
    
    def get_instruction_for_document_type(self, document_type: str) -> str:
        """
        Get specialized instruction based on document type.
        
        Args:
            document_type: Type of document (report, proposal, email, etc.)
            
        Returns:
            Specialized instruction for the document type
        """
        type_specific_instructions = {
            "report": (
                "This appears to be a report. Focus on extracting the executive summary, "
                "key findings, recommendations, and methodology. Identify the report type "
                "(financial, technical, research, etc.) and main stakeholders."
            ),
            "proposal": (
                "This appears to be a proposal. Focus on the proposed solution, "
                "objectives, timeline, budget considerations, and key stakeholders. "
                "Identify what is being proposed and to whom."
            ),
            "email": (
                "This appears to be an email or communication. Focus on the main purpose, "
                "action items, decisions made, and people involved. Identify the sender, "
                "recipients, and key discussion points."
            ),
            "contract": (
                "This appears to be a contract or agreement. Focus on the parties involved, "
                "main terms, obligations, deliverables, and timeline. Identify the type "
                "of agreement and key contractual elements."
            ),
            "policy": (
                "This appears to be a policy document. Focus on the policy objectives, "
                "scope, key requirements, compliance aspects, and affected stakeholders. "
                "Identify the policy area and implementation details."
            ),
            "procedure": (
                "This appears to be a procedure or process document. Focus on the process "
                "steps, roles and responsibilities, inputs/outputs, and quality requirements. "
                "Identify the process owner and key stakeholders."
            ),
            "meeting_notes": (
                "This appears to be meeting notes or minutes. Focus on attendees, "
                "key decisions, action items, discussion topics, and next steps. "
                "Identify the meeting purpose and outcomes."
            ),
        }
        
        specific_instruction = type_specific_instructions.get(
            document_type.lower(), 
            self.custom_instruction
        )
        
        return f"{specific_instruction}\n\n{self._get_default_instruction()}"
    
    def create_specialized_extraction_function(
        self, 
        document_type: str
    ) -> cocoindex.functions.ExtractByLlm:
        """
        Create a specialized extraction function for a specific document type.
        
        Args:
            document_type: Type of document for specialized processing
            
        Returns:
            Configured ExtractByLlm function with specialized instruction
        """
        specialized_instruction = self.get_instruction_for_document_type(document_type)
        
        return cocoindex.functions.ExtractByLlm(
            llm_spec=self.llm_spec,
            output_type=DocumentSummary,
            instruction=specialized_instruction
        )


def create_default_summarizer(
    api_type: cocoindex.LlmApiType = cocoindex.LlmApiType.OPENAI,
    model: str = "gpt-4o"
) -> DocumentSummarizer:
    """
    Create a default document summarizer with common settings.
    
    Args:
        api_type: LLM API type (default: OpenAI)
        model: Model name (default: gpt-4o)
        
    Returns:
        Configured DocumentSummarizer instance
    """
    llm_spec = cocoindex.LlmSpec(
        api_type=api_type,
        model=model
    )
    
    return DocumentSummarizer(llm_spec=llm_spec)


def create_summarizer_with_custom_instruction(
    instruction: str,
    api_type: cocoindex.LlmApiType = cocoindex.LlmApiType.OPENAI,
    model: str = "gpt-4o"
) -> DocumentSummarizer:
    """
    Create a document summarizer with custom instruction.
    
    Args:
        instruction: Custom instruction for summarization
        api_type: LLM API type (default: OpenAI)
        model: Model name (default: gpt-4o)
        
    Returns:
        Configured DocumentSummarizer instance with custom instruction
    """
    llm_spec = cocoindex.LlmSpec(
        api_type=api_type,
        model=model
    )
    
    return DocumentSummarizer(
        llm_spec=llm_spec,
        custom_instruction=instruction
    )
