"""
Core state management for enterprise knowledge graph pipeline.

This module provides the main state management functionality,
including job creation, status tracking, and error handling.
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
import json

from .database import DatabaseManager
from .models import (
    Organization, DataSource, File, ProcessingJob, PipelineStage,
    StageExecution, ErrorLog, ProcessingStatus, DataSourceType, StageType
)

logger = logging.getLogger(__name__)


class StateManager:
    """
    Core state management for the enterprise KG pipeline.
    
    Handles job creation, status updates, error tracking, and recovery operations.
    """
    
    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize state manager.
        
        Args:
            db_manager: Database manager instance
        """
        self.db = db_manager
    
    def create_organization(
        self,
        org_id: str,
        name: str,
        description: Optional[str] = None,
        neo4j_config: Optional[Dict] = None,
        pinecone_config: Optional[Dict] = None,
        llm_config: Optional[Dict] = None
    ) -> Organization:
        """
        Create or update an organization.
        
        Args:
            org_id: Unique organization identifier
            name: Organization name
            description: Optional description
            neo4j_config: Neo4j configuration
            pinecone_config: Pinecone configuration
            llm_config: LLM configuration
            
        Returns:
            Organization instance
        """
        try:
            with self.db.get_session() as session:
                # Check if organization exists
                org = session.query(Organization).filter_by(id=org_id).first()
                
                if org:
                    # Update existing organization
                    org.name = name
                    org.description = description
                    org.neo4j_config = neo4j_config
                    org.pinecone_config = pinecone_config
                    org.llm_config = llm_config
                    org.updated_at = datetime.utcnow()
                    logger.info(f"Updated organization: {org_id}")
                else:
                    # Create new organization
                    org = Organization(
                        id=org_id,
                        name=name,
                        description=description,
                        neo4j_config=neo4j_config,
                        pinecone_config=pinecone_config,
                        llm_config=llm_config
                    )
                    session.add(org)
                    logger.info(f"Created organization: {org_id}")
                
                session.commit()
                return org
                
        except Exception as e:
            logger.error(f"Failed to create/update organization {org_id}: {e}")
            raise
    
    def add_data_source(
        self,
        org_id: str,
        source_id: str,
        name: str,
        source_type: DataSourceType,
        config: Dict[str, Any],
        credentials: Optional[Dict[str, Any]] = None
    ) -> DataSource:
        """
        Add a data source to an organization.
        
        Args:
            org_id: Organization ID
            source_id: Unique data source ID
            name: Data source name
            source_type: Type of data source
            config: Source configuration
            credentials: Source credentials (will be encrypted)
            
        Returns:
            DataSource instance
        """
        try:
            with self.db.get_session() as session:
                # Verify organization exists
                org = session.query(Organization).filter_by(id=org_id).first()
                if not org:
                    raise ValueError(f"Organization {org_id} not found")
                
                # Check if data source exists
                data_source = session.query(DataSource).filter_by(id=source_id).first()
                
                if data_source:
                    # Update existing data source
                    data_source.name = name
                    data_source.type = source_type
                    data_source.config = config
                    data_source.credentials = credentials
                    data_source.updated_at = datetime.utcnow()
                    logger.info(f"Updated data source: {source_id}")
                else:
                    # Create new data source
                    data_source = DataSource(
                        id=source_id,
                        organization_id=org_id,
                        name=name,
                        type=source_type,
                        config=config,
                        credentials=credentials
                    )
                    session.add(data_source)
                    logger.info(f"Created data source: {source_id}")
                
                session.commit()
                return data_source
                
        except Exception as e:
            logger.error(f"Failed to add data source {source_id}: {e}")
            raise
    
    def register_file(
        self,
        file_id: str,
        org_id: str,
        data_source_id: str,
        filename: str,
        content: Optional[str] = None,
        file_path: Optional[str] = None,
        file_size: Optional[int] = None,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> File:
        """
        Register a file for processing.
        
        Args:
            file_id: Unique file identifier
            org_id: Organization ID
            data_source_id: Data source ID
            filename: File name
            content: File content (for text files)
            file_path: File path
            file_size: File size in bytes
            content_type: MIME type
            metadata: Additional metadata
            
        Returns:
            File instance
        """
        try:
            with self.db.get_session() as session:
                # Verify organization and data source exist
                org = session.query(Organization).filter_by(id=org_id).first()
                if not org:
                    raise ValueError(f"Organization {org_id} not found")
                
                data_source = session.query(DataSource).filter_by(id=data_source_id).first()
                if not data_source:
                    raise ValueError(f"Data source {data_source_id} not found")
                
                # Generate content hash if content provided
                content_hash = None
                if content:
                    import hashlib
                    content_hash = hashlib.sha256(content.encode()).hexdigest()
                
                # Check if file exists
                file_obj = session.query(File).filter_by(id=file_id).first()
                
                if file_obj:
                    # Update existing file
                    file_obj.filename = filename
                    file_obj.content = content
                    file_obj.file_path = file_path
                    file_obj.file_size = file_size
                    file_obj.content_type = content_type
                    file_obj.content_hash = content_hash
                    file_obj.metadata = metadata
                    file_obj.updated_at = datetime.utcnow()
                    logger.info(f"Updated file: {file_id}")
                else:
                    # Create new file
                    file_obj = File(
                        id=file_id,
                        organization_id=org_id,
                        data_source_id=data_source_id,
                        filename=filename,
                        content=content,
                        file_path=file_path,
                        file_size=file_size,
                        content_type=content_type,
                        content_hash=content_hash,
                        metadata=metadata
                    )
                    session.add(file_obj)
                    logger.info(f"Registered file: {file_id}")
                
                session.commit()
                return file_obj
                
        except Exception as e:
            logger.error(f"Failed to register file {file_id}: {e}")
            raise
    
    def create_processing_job(
        self,
        org_id: str,
        file_id: str,
        job_type: str = "full_pipeline",
        config: Optional[Dict[str, Any]] = None
    ) -> ProcessingJob:
        """
        Create a new processing job.
        
        Args:
            org_id: Organization ID
            file_id: File ID to process
            job_type: Type of job (full_pipeline, retry_stage, etc.)
            config: Job configuration
            
        Returns:
            ProcessingJob instance
        """
        try:
            with self.db.get_session() as session:
                # Verify file exists
                file_obj = session.query(File).filter_by(id=file_id, organization_id=org_id).first()
                if not file_obj:
                    raise ValueError(f"File {file_id} not found in organization {org_id}")
                
                # Create job
                job_id = str(uuid.uuid4())
                job = ProcessingJob(
                    id=job_id,
                    organization_id=org_id,
                    file_id=file_id,
                    job_type=job_type,
                    config=config or {}
                )
                session.add(job)
                
                # Create stage executions for all stages
                stages = session.query(PipelineStage).order_by(PipelineStage.order_index).all()
                for stage in stages:
                    execution = StageExecution(
                        id=str(uuid.uuid4()),
                        job_id=job_id,
                        stage_id=stage.id
                    )
                    session.add(execution)
                
                session.commit()
                logger.info(f"Created processing job: {job_id} for file: {file_id}")
                return job
                
        except Exception as e:
            logger.error(f"Failed to create processing job for file {file_id}: {e}")
            raise
    
    def update_job_status(
        self,
        job_id: str,
        status: ProcessingStatus,
        current_stage: Optional[StageType] = None,
        progress_percentage: Optional[int] = None,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None
    ):
        """
        Update job status.
        
        Args:
            job_id: Job ID
            status: New status
            current_stage: Current stage being processed
            progress_percentage: Progress percentage (0-100)
            result: Job results
            error_message: Error message if failed
        """
        try:
            with self.db.get_session() as session:
                job = session.query(ProcessingJob).filter_by(id=job_id).first()
                if not job:
                    raise ValueError(f"Job {job_id} not found")
                
                job.status = status
                if current_stage:
                    job.current_stage = current_stage
                if progress_percentage is not None:
                    job.progress_percentage = progress_percentage
                if result:
                    job.result = result
                if error_message:
                    job.error_message = error_message
                
                # Update timestamps
                if status == ProcessingStatus.RUNNING and not job.started_at:
                    job.started_at = datetime.utcnow()
                elif status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED, ProcessingStatus.CANCELLED]:
                    job.completed_at = datetime.utcnow()
                
                session.commit()
                logger.info(f"Updated job {job_id} status to {status.value}")
                
        except Exception as e:
            logger.error(f"Failed to update job status for {job_id}: {e}")
            raise

    def update_stage_execution(
        self,
        execution_id: str,
        status: ProcessingStatus,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        logs: Optional[str] = None
    ):
        """
        Update stage execution status.

        Args:
            execution_id: Stage execution ID
            status: New status
            result: Execution results
            error_message: Error message if failed
            logs: Execution logs
        """
        try:
            with self.db.get_session() as session:
                execution = session.query(StageExecution).filter_by(id=execution_id).first()
                if not execution:
                    raise ValueError(f"Stage execution {execution_id} not found")

                execution.status = status
                if result:
                    execution.result = result
                if error_message:
                    execution.error_message = error_message
                if logs:
                    execution.logs = logs

                # Update timestamps
                if status == ProcessingStatus.RUNNING and not execution.started_at:
                    execution.started_at = datetime.utcnow()
                elif status in [ProcessingStatus.COMPLETED, ProcessingStatus.FAILED]:
                    execution.completed_at = datetime.utcnow()

                session.commit()
                logger.info(f"Updated stage execution {execution_id} status to {status.value}")

        except Exception as e:
            logger.error(f"Failed to update stage execution {execution_id}: {e}")
            raise

    def log_error(
        self,
        stage_execution_id: str,
        error_type: str,
        error_message: str,
        stack_trace: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
        is_recoverable: bool = True,
        recovery_suggestion: Optional[str] = None
    ) -> ErrorLog:
        """
        Log an error for a stage execution.

        Args:
            stage_execution_id: Stage execution ID
            error_type: Type of error
            error_message: Error message
            stack_trace: Stack trace
            context: Additional context
            is_recoverable: Whether error is recoverable
            recovery_suggestion: Suggestion for recovery

        Returns:
            ErrorLog instance
        """
        try:
            with self.db.get_session() as session:
                error_log = ErrorLog(
                    id=str(uuid.uuid4()),
                    stage_execution_id=stage_execution_id,
                    error_type=error_type,
                    error_message=error_message,
                    stack_trace=stack_trace,
                    context=context,
                    is_recoverable=is_recoverable,
                    recovery_suggestion=recovery_suggestion
                )
                session.add(error_log)
                session.commit()

                logger.error(f"Logged error for stage execution {stage_execution_id}: {error_type}")
                return error_log

        except Exception as e:
            logger.error(f"Failed to log error for stage execution {stage_execution_id}: {e}")
            raise

    def get_job_status(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive job status.

        Args:
            job_id: Job ID

        Returns:
            Job status dictionary or None if not found
        """
        try:
            with self.db.get_session() as session:
                job = session.query(ProcessingJob).filter_by(id=job_id).first()
                if not job:
                    return None

                # Get stage executions
                executions = session.query(StageExecution).filter_by(job_id=job_id).all()

                stage_status = []
                for execution in executions:
                    stage_info = {
                        "execution_id": execution.id,
                        "stage_id": execution.stage_id,
                        "stage_name": execution.stage.name if execution.stage else "Unknown",
                        "status": execution.status.value,
                        "attempt_number": execution.attempt_number,
                        "started_at": execution.started_at.isoformat() if execution.started_at else None,
                        "completed_at": execution.completed_at.isoformat() if execution.completed_at else None,
                        "error_message": execution.error_message,
                        "result": execution.result
                    }
                    stage_status.append(stage_info)

                return {
                    "job_id": job.id,
                    "organization_id": job.organization_id,
                    "file_id": job.file_id,
                    "job_type": job.job_type,
                    "status": job.status.value,
                    "current_stage": job.current_stage.value if job.current_stage else None,
                    "progress_percentage": job.progress_percentage,
                    "created_at": job.created_at.isoformat(),
                    "started_at": job.started_at.isoformat() if job.started_at else None,
                    "completed_at": job.completed_at.isoformat() if job.completed_at else None,
                    "result": job.result,
                    "error_message": job.error_message,
                    "stages": stage_status
                }

        except Exception as e:
            logger.error(f"Failed to get job status for {job_id}: {e}")
            return None

    def get_organization_status(self, org_id: str) -> Dict[str, Any]:
        """
        Get comprehensive organization processing status.

        Args:
            org_id: Organization ID

        Returns:
            Organization status dictionary
        """
        try:
            with self.db.get_session() as session:
                org = session.query(Organization).filter_by(id=org_id).first()
                if not org:
                    return {"error": f"Organization {org_id} not found"}

                # Get file counts by status
                file_counts = {}
                for status in ProcessingStatus:
                    count = session.query(File).filter_by(
                        organization_id=org_id,
                        status=status
                    ).count()
                    file_counts[status.value] = count

                # Get job counts by status
                job_counts = {}
                for status in ProcessingStatus:
                    count = session.query(ProcessingJob).filter_by(
                        organization_id=org_id,
                        status=status
                    ).count()
                    job_counts[status.value] = count

                # Get recent jobs
                recent_jobs = session.query(ProcessingJob).filter_by(
                    organization_id=org_id
                ).order_by(ProcessingJob.created_at.desc()).limit(10).all()

                recent_job_info = []
                for job in recent_jobs:
                    recent_job_info.append({
                        "job_id": job.id,
                        "file_id": job.file_id,
                        "status": job.status.value,
                        "created_at": job.created_at.isoformat(),
                        "progress_percentage": job.progress_percentage
                    })

                return {
                    "organization_id": org.id,
                    "organization_name": org.name,
                    "file_counts": file_counts,
                    "job_counts": job_counts,
                    "recent_jobs": recent_job_info,
                    "total_files": sum(file_counts.values()),
                    "total_jobs": sum(job_counts.values())
                }

        except Exception as e:
            logger.error(f"Failed to get organization status for {org_id}: {e}")
            return {"error": str(e)}
