-- Initial schema for Enterprise KG Pipeline State Management
-- PostgreSQL database schema

-- <PERSON>reate enum types
CREATE TYPE processing_status AS ENUM (
    'pending',
    'running', 
    'completed',
    'failed',
    'retrying',
    'cancelled'
);

CREATE TYPE data_source_type AS ENUM (
    'slack',
    'jira',
    'files',
    'confluence',
    'sharepoint',
    'google_drive'
);

CREATE TYPE stage_type AS ENUM (
    'data_source_connection',
    'file_attachment',
    'document_processing',
    'graph_generation',
    'vector_storage'
);

-- Organizations table
CREATE TABLE organizations (
    id VARCHAR(255) PRIMARY KEY,
    name VARCHAR(500) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    neo4j_config JSONB,
    pinecone_config JSONB,
    llm_config JSONB
);

-- Data sources table
CREATE TABLE data_sources (
    id VARCHAR(255) PRIMARY KEY,
    organization_id VARCHAR(255) NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(500) NOT NULL,
    type data_source_type NOT NULL,
    config JSONB,
    credentials JSONB,
    status processing_status DEFAULT 'pending',
    last_sync TIMESTAMP,
    next_sync TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Files table
CREATE TABLE files (
    id VARCHAR(255) PRIMARY KEY,
    organization_id VARCHAR(255) NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    data_source_id VARCHAR(255) NOT NULL REFERENCES data_sources(id) ON DELETE CASCADE,
    filename VARCHAR(1000) NOT NULL,
    file_path TEXT,
    file_size INTEGER,
    content_type VARCHAR(100),
    content_hash VARCHAR(64),
    content TEXT,
    metadata JSONB,
    status processing_status DEFAULT 'pending',
    neo4j_node_created BOOLEAN DEFAULT FALSE,
    chunks_created INTEGER DEFAULT 0,
    embeddings_created BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_processed TIMESTAMP
);

-- Pipeline stages table
CREATE TABLE pipeline_stages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL UNIQUE,
    type stage_type NOT NULL,
    description TEXT,
    order_index INTEGER NOT NULL,
    is_parallel BOOLEAN DEFAULT FALSE,
    retry_count INTEGER DEFAULT 3,
    timeout_seconds INTEGER DEFAULT 3600,
    depends_on JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Processing jobs table
CREATE TABLE processing_jobs (
    id VARCHAR(255) PRIMARY KEY,
    organization_id VARCHAR(255) NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    file_id VARCHAR(255) NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    job_type VARCHAR(100) NOT NULL,
    config JSONB,
    status processing_status DEFAULT 'pending',
    current_stage stage_type,
    progress_percentage INTEGER DEFAULT 0,
    result JSONB,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- Stage executions table
CREATE TABLE stage_executions (
    id VARCHAR(255) PRIMARY KEY,
    job_id VARCHAR(255) NOT NULL REFERENCES processing_jobs(id) ON DELETE CASCADE,
    stage_id INTEGER NOT NULL REFERENCES pipeline_stages(id),
    status processing_status DEFAULT 'pending',
    attempt_number INTEGER DEFAULT 1,
    result JSONB,
    error_message TEXT,
    logs TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    started_at TIMESTAMP,
    completed_at TIMESTAMP
);

-- Error logs table
CREATE TABLE error_logs (
    id VARCHAR(255) PRIMARY KEY,
    stage_execution_id VARCHAR(255) REFERENCES stage_executions(id) ON DELETE CASCADE,
    error_type VARCHAR(200),
    error_message TEXT,
    stack_trace TEXT,
    context JSONB,
    is_recoverable BOOLEAN DEFAULT TRUE,
    recovery_suggestion TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX idx_files_org_status ON files(organization_id, status);
CREATE INDEX idx_jobs_org_status ON processing_jobs(organization_id, status);
CREATE INDEX idx_stage_exec_job_stage ON stage_executions(job_id, stage_id);
CREATE INDEX idx_error_logs_stage ON error_logs(stage_execution_id);
CREATE INDEX idx_data_sources_org ON data_sources(organization_id);
CREATE INDEX idx_files_data_source ON files(data_source_id);
CREATE INDEX idx_jobs_file ON processing_jobs(file_id);
CREATE INDEX idx_stage_exec_status ON stage_executions(status);
CREATE INDEX idx_jobs_created_at ON processing_jobs(created_at);
CREATE INDEX idx_error_logs_created_at ON error_logs(created_at);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_organizations_updated_at 
    BEFORE UPDATE ON organizations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_data_sources_updated_at 
    BEFORE UPDATE ON data_sources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_files_updated_at 
    BEFORE UPDATE ON files 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default pipeline stages
INSERT INTO pipeline_stages (name, type, description, order_index, is_parallel, retry_count, timeout_seconds, depends_on) VALUES
('Data Source Connection', 'data_source_connection', 'Connect to data sources and collect files/content', 1, true, 3, 1800, '[]'),
('File Attachment', 'file_attachment', 'Attach files to organization with contains relations', 2, true, 3, 600, '[1]'),
('Document Processing', 'document_processing', 'Process files through enterprise_kg_minimal module', 3, true, 2, 3600, '[2]'),
('Graph Generation', 'graph_generation', 'Convert chunks to graphs and attach to files', 4, true, 2, 3600, '[3]'),
('Vector Storage', 'vector_storage', 'Insert chunks into Pinecone with embeddings', 5, true, 3, 1800, '[3]');

-- Create a view for job status summary
CREATE VIEW job_status_summary AS
SELECT 
    organization_id,
    status,
    COUNT(*) as job_count,
    AVG(progress_percentage) as avg_progress,
    MIN(created_at) as earliest_job,
    MAX(created_at) as latest_job
FROM processing_jobs 
GROUP BY organization_id, status;

-- Create a view for file processing summary
CREATE VIEW file_processing_summary AS
SELECT 
    f.organization_id,
    ds.type as data_source_type,
    f.status,
    COUNT(*) as file_count,
    SUM(f.file_size) as total_size,
    SUM(f.chunks_created) as total_chunks,
    COUNT(CASE WHEN f.neo4j_node_created THEN 1 END) as nodes_created,
    COUNT(CASE WHEN f.embeddings_created THEN 1 END) as embeddings_created
FROM files f
JOIN data_sources ds ON f.data_source_id = ds.id
GROUP BY f.organization_id, ds.type, f.status;

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO your_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO your_app_user;
