"""
Database connection and management for pipeline state.

This module handles PostgreSQL database connections, schema creation,
and basic database operations for the pipeline state management system.
"""

import logging
from typing import Dict, Any, Optional
from contextlib import contextmanager

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .models import Base, PipelineStage, StageType

logger = logging.getLogger(__name__)


class DatabaseManager:
    """
    Manages PostgreSQL database connections and operations for pipeline state.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Initialize database manager.
        
        Args:
            config: Database configuration with keys:
                - host: PostgreSQL host
                - port: PostgreSQL port
                - database: Database name
                - username: Username
                - password: Password
                - pool_size: Connection pool size (optional)
                - max_overflow: Max overflow connections (optional)
        """
        self.config = config
        self.engine = None
        self.SessionLocal = None
        self._initialize_engine()
    
    def _initialize_engine(self):
        """Initialize SQLAlchemy engine and session factory."""
        try:
            # Build connection URL
            url = (
                f"postgresql://{self.config['username']}:{self.config['password']}"
                f"@{self.config['host']}:{self.config.get('port', 5432)}"
                f"/{self.config['database']}"
            )
            
            # Engine configuration
            engine_config = {
                "echo": self.config.get("echo", False),
                "pool_size": self.config.get("pool_size", 10),
                "max_overflow": self.config.get("max_overflow", 20),
                "pool_pre_ping": True,
                "pool_recycle": 3600
            }
            
            self.engine = create_engine(url, **engine_config)
            self.SessionLocal = sessionmaker(bind=self.engine)
            
            logger.info("Database engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database engine: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """
        Get a database session with automatic cleanup.
        
        Yields:
            Session: SQLAlchemy session
        """
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
            self._initialize_default_stages()
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    def drop_tables(self):
        """Drop all database tables. Use with caution!"""
        try:
            Base.metadata.drop_all(bind=self.engine)
            logger.info("Database tables dropped successfully")
        except Exception as e:
            logger.error(f"Failed to drop database tables: {e}")
            raise
    
    def test_connection(self) -> bool:
        """
        Test database connection.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection test successful")
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def _initialize_default_stages(self):
        """Initialize default pipeline stages."""
        default_stages = [
            {
                "name": "Data Source Connection",
                "type": StageType.DATA_SOURCE_CONNECTION,
                "description": "Connect to data sources and collect files/content",
                "order_index": 1,
                "is_parallel": True,
                "retry_count": 3,
                "timeout_seconds": 1800,
                "depends_on": []
            },
            {
                "name": "File Attachment",
                "type": StageType.FILE_ATTACHMENT,
                "description": "Attach files to organization with contains relations",
                "order_index": 2,
                "is_parallel": True,
                "retry_count": 3,
                "timeout_seconds": 600,
                "depends_on": [1]
            },
            {
                "name": "Document Processing",
                "type": StageType.DOCUMENT_PROCESSING,
                "description": "Process files through enterprise_kg_minimal module",
                "order_index": 3,
                "is_parallel": True,
                "retry_count": 2,
                "timeout_seconds": 3600,
                "depends_on": [2]
            },
            {
                "name": "Graph Generation",
                "type": StageType.GRAPH_GENERATION,
                "description": "Convert chunks to graphs and attach to files",
                "order_index": 4,
                "is_parallel": True,
                "retry_count": 2,
                "timeout_seconds": 3600,
                "depends_on": [3]
            },
            {
                "name": "Vector Storage",
                "type": StageType.VECTOR_STORAGE,
                "description": "Insert chunks into Pinecone with embeddings",
                "order_index": 5,
                "is_parallel": True,
                "retry_count": 3,
                "timeout_seconds": 1800,
                "depends_on": [3]  # Can run parallel with graph generation
            }
        ]
        
        try:
            with self.get_session() as session:
                # Check if stages already exist
                existing_count = session.query(PipelineStage).count()
                if existing_count > 0:
                    logger.info(f"Pipeline stages already exist ({existing_count} stages)")
                    return
                
                # Create default stages
                for stage_data in default_stages:
                    stage = PipelineStage(**stage_data)
                    session.add(stage)
                
                session.commit()
                logger.info(f"Created {len(default_stages)} default pipeline stages")
                
        except Exception as e:
            logger.error(f"Failed to initialize default stages: {e}")
            raise
    
    def get_database_stats(self) -> Dict[str, Any]:
        """
        Get database statistics.
        
        Returns:
            Dict with database statistics
        """
        try:
            with self.get_session() as session:
                from .models import Organization, File, ProcessingJob, StageExecution
                
                stats = {
                    "organizations": session.query(Organization).count(),
                    "files": session.query(File).count(),
                    "processing_jobs": session.query(ProcessingJob).count(),
                    "stage_executions": session.query(StageExecution).count(),
                    "pipeline_stages": session.query(PipelineStage).count()
                }
                
                return stats
                
        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}
    
    def cleanup_old_data(self, days_old: int = 30):
        """
        Clean up old processing data.
        
        Args:
            days_old: Remove data older than this many days
        """
        try:
            with self.get_session() as session:
                from datetime import datetime, timedelta
                from .models import ProcessingJob, StageExecution, ErrorLog
                
                cutoff_date = datetime.utcnow() - timedelta(days=days_old)
                
                # Delete old completed jobs and their executions
                old_jobs = session.query(ProcessingJob).filter(
                    ProcessingJob.completed_at < cutoff_date,
                    ProcessingJob.status.in_(["completed", "failed", "cancelled"])
                ).all()
                
                deleted_count = 0
                for job in old_jobs:
                    # Delete related stage executions and error logs
                    for execution in job.stage_executions:
                        session.delete(execution)
                    session.delete(job)
                    deleted_count += 1
                
                session.commit()
                logger.info(f"Cleaned up {deleted_count} old processing jobs")
                
        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            raise


def create_database_manager(config: Dict[str, Any]) -> DatabaseManager:
    """
    Create and initialize a database manager.
    
    Args:
        config: Database configuration
        
    Returns:
        DatabaseManager instance
    """
    return DatabaseManager(config)
