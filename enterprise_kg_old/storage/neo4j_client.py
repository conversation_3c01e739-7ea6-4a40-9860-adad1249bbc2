"""
Neo4j graph database client for Enterprise KG

This module provides integration with Neo4j for storing and querying
knowledge graph data. Can work standalone or with CocoIndex.
"""

# import cocoindex  # Commented out for standalone usage
from dataclasses import dataclass
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from ..constants.schemas import GraphNode, GraphRelationship, EntityRelationship


@dataclass
class Neo4jConnection:
    """Connection specification for Neo4j."""

    uri: str
    user: str
    password: str
    database: Optional[str] = None


class Neo4jClient:
    """
    Client for interacting with Neo4j graph database.

    This class provides methods for storing and querying knowledge graph data
    following the CocoIndex Neo4j storage pattern.
    """

    def __init__(self, connection: Neo4jConnection):
        """
        Initialize the Neo4j client.

        Args:
            connection: Neo4j connection configuration
        """
        self.connection = connection
        self._driver = None

    def _get_driver(self):
        """Get or create Neo4j driver."""
        if self._driver is None:
            try:
                from neo4j import GraphDatabase
                self._driver = GraphDatabase.driver(
                    self.connection.uri,
                    auth=(self.connection.user, self.connection.password)
                )
            except ImportError:
                raise ImportError(
                    "Neo4j driver not installed. Install with: pip install neo4j"
                )
        return self._driver

    def close(self):
        """Close the Neo4j driver connection."""
        if self._driver:
            self._driver.close()
            self._driver = None

    def create_node(self, node: GraphNode) -> Dict[str, Any]:
        """
        Create a node in the Neo4j database.

        Args:
            node: Graph node to create

        Returns:
            Created node information
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            query = f"""
            MERGE (n:{node.label} {{name: $name}})
            SET n += $properties
            SET n.created_at = $created_at
            SET n.updated_at = $updated_at
            RETURN n
            """

            result = session.run(
                query,
                name=node.name,
                properties=node.properties,
                created_at=node.created_at or datetime.now(),
                updated_at=datetime.now()
            )

            return result.single()["n"]

    def create_relationship(self, relationship: GraphRelationship) -> Dict[str, Any]:
        """
        Create a relationship in the Neo4j database.

        Args:
            relationship: Graph relationship to create

        Returns:
            Created relationship information
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            query = f"""
            MATCH (source {{node_id: $source_node_id}})
            MATCH (target {{node_id: $target_node_id}})
            MERGE (source)-[r:{relationship.relationship_type}]->(target)
            SET r += $properties
            SET r.created_at = $created_at
            SET r.updated_at = $updated_at
            RETURN r
            """

            result = session.run(
                query,
                source_node_id=relationship.source_node_id,
                target_node_id=relationship.target_node_id,
                properties=relationship.properties,
                created_at=relationship.created_at or datetime.now(),
                updated_at=datetime.now()
            )

            return result.single()["r"]

    def create_entity_relationship(
        self,
        entity_rel: EntityRelationship,
        source_document: Optional[str] = None
    ) -> Tuple[Dict[str, Any], Dict[str, Any], Dict[str, Any]]:
        """
        Create nodes and relationship from EntityRelationship.

        Args:
            entity_rel: Entity relationship to create
            source_document: Source document filename

        Returns:
            Tuple of (source_node, target_node, relationship)
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            # Create or merge source node
            source_query = """
            MERGE (source:Entity {name: $subject})
            SET source.updated_at = $updated_at
            ON CREATE SET source.created_at = $created_at
            RETURN source
            """

            # Create or merge target node
            target_query = """
            MERGE (target:Entity {name: $object})
            SET target.updated_at = $updated_at
            ON CREATE SET target.created_at = $created_at
            RETURN target
            """

            # Create relationship
            rel_query = f"""
            MATCH (source:Entity {{name: $subject}})
            MATCH (target:Entity {{name: $object}})
            MERGE (source)-[r:{entity_rel.predicate.upper()}]->(target)
            SET r.confidence_score = $confidence_score
            SET r.context = $context
            SET r.source_sentence = $source_sentence
            SET r.source_document = $source_document
            SET r.updated_at = $updated_at
            ON CREATE SET r.created_at = $created_at
            RETURN r
            """

            now = datetime.now()

            # Execute queries
            source_result = session.run(
                source_query,
                subject=entity_rel.subject,
                created_at=now,
                updated_at=now
            )

            target_result = session.run(
                target_query,
                object=entity_rel.object,
                created_at=now,
                updated_at=now
            )

            rel_result = session.run(
                rel_query,
                subject=entity_rel.subject,
                object=entity_rel.object,
                confidence_score=entity_rel.confidence_score,
                context=entity_rel.context,
                source_sentence=entity_rel.source_sentence,
                source_document=source_document,
                created_at=now,
                updated_at=now
            )

            return (
                source_result.single()["source"],
                target_result.single()["target"],
                rel_result.single()["r"]
            )

    def batch_create_entity_relationships(
        self,
        entity_relationships: List[EntityRelationship],
        source_document: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Create multiple entity relationships in batch.

        Args:
            entity_relationships: List of entity relationships to create
            source_document: Source document filename

        Returns:
            List of creation results
        """
        results = []

        for entity_rel in entity_relationships:
            try:
                result = self.create_entity_relationship(entity_rel, source_document)
                results.append({
                    "success": True,
                    "entity_relationship": entity_rel,
                    "result": result
                })
            except Exception as e:
                results.append({
                    "success": False,
                    "entity_relationship": entity_rel,
                    "error": str(e)
                })

        return results

    def query_entities(
        self,
        entity_name: Optional[str] = None,
        entity_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Query entities from the graph.

        Args:
            entity_name: Optional entity name filter
            entity_type: Optional entity type filter
            limit: Maximum number of results

        Returns:
            List of matching entities
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            conditions = []
            params = {"limit": limit}

            if entity_name:
                conditions.append("n.name CONTAINS $entity_name")
                params["entity_name"] = entity_name

            if entity_type:
                conditions.append(f"n:{entity_type}")

            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
            MATCH (n)
            {where_clause}
            RETURN n
            LIMIT $limit
            """

            result = session.run(query, **params)
            return [record["n"] for record in result]

    def query_relationships(
        self,
        source_entity: Optional[str] = None,
        target_entity: Optional[str] = None,
        relationship_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        Query relationships from the graph.

        Args:
            source_entity: Optional source entity name filter
            target_entity: Optional target entity name filter
            relationship_type: Optional relationship type filter
            limit: Maximum number of results

        Returns:
            List of matching relationships with source and target nodes
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            conditions = []
            params = {"limit": limit}

            if source_entity:
                conditions.append("source.name = $source_entity")
                params["source_entity"] = source_entity

            if target_entity:
                conditions.append("target.name = $target_entity")
                params["target_entity"] = target_entity

            rel_pattern = f"[r:{relationship_type}]" if relationship_type else "[r]"
            where_clause = "WHERE " + " AND ".join(conditions) if conditions else ""

            query = f"""
            MATCH (source)-{rel_pattern}->(target)
            {where_clause}
            RETURN source, r, target
            LIMIT $limit
            """

            result = session.run(query, **params)
            return [
                {
                    "source": record["source"],
                    "relationship": record["r"],
                    "target": record["target"]
                }
                for record in result
            ]

    def get_entity_neighbors(
        self,
        entity_name: str,
        relationship_types: Optional[List[str]] = None,
        direction: str = "both",  # "in", "out", "both"
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get neighboring entities for a given entity.

        Args:
            entity_name: Name of the central entity
            relationship_types: Optional list of relationship types to filter
            direction: Direction of relationships ("in", "out", "both")
            limit: Maximum number of results

        Returns:
            List of neighboring entities with relationship information
        """
        driver = self._get_driver()

        with driver.session(database=self.connection.database) as session:
            rel_types_filter = ""
            if relationship_types:
                rel_types = "|".join(relationship_types)
                rel_types_filter = f":{rel_types}"

            if direction == "out":
                pattern = f"(entity)-[r{rel_types_filter}]->(neighbor)"
            elif direction == "in":
                pattern = f"(entity)<-[r{rel_types_filter}]-(neighbor)"
            else:  # both
                pattern = f"(entity)-[r{rel_types_filter}]-(neighbor)"

            query = f"""
            MATCH {pattern}
            WHERE entity.name = $entity_name
            RETURN neighbor, r, type(r) as relationship_type
            LIMIT $limit
            """

            result = session.run(query, entity_name=entity_name, limit=limit)
            return [
                {
                    "neighbor": record["neighbor"],
                    "relationship": record["r"],
                    "relationship_type": record["relationship_type"]
                }
                for record in result
            ]


# CocoIndex integration functions (commented out for standalone usage)
"""
def create_neo4j_storage_spec(
    connection: Neo4jConnection,
    node_label: str = "Entity",
    relationship_mapping: Optional[Dict[str, str]] = None
):
    # This function requires CocoIndex - commented out for standalone usage
    # Uncomment when integrating with CocoIndex
    pass
"""


def create_default_neo4j_client(
    uri: str = "bolt://localhost:7687",
    user: str = "neo4j",
    password: str = "cocoindex",
    database: Optional[str] = None
) -> Neo4jClient:
    """
    Create a default Neo4j client with standard settings.

    Args:
        uri: Neo4j URI
        user: Neo4j username
        password: Neo4j password
        database: Optional database name

    Returns:
        Configured Neo4jClient instance
    """
    connection = Neo4jConnection(
        uri=uri,
        user=user,
        password=password,
        database=database
    )

    return Neo4jClient(connection)
