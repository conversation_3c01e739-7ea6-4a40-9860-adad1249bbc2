"""
Dynamic Prompt Generator

This module generates LLM prompts dynamically based on the constants
defined in the entities and relationships files. This ensures prompts
are always up-to-date with the latest entity and relationship types.
"""

from typing import List, Set, Dict, Any
from constants.entities import (
    get_all_entity_types, 
    get_person_related_types,
    get_project_related_types,
    get_organizational_types,
    get_system_related_types,
    get_document_related_types
)
from constants.relationships import (
    get_all_relationship_types,
    get_project_relationships,
    get_organizational_relationships,
    get_document_relationships,
    get_common_entity_relationship_patterns
)


class PromptGenerator:
    """
    Generates dynamic prompts for entity and relationship extraction
    based on the constants defined in the system.
    """
    
    def __init__(
        self,
        focus_entities: List[str] = None,
        focus_relationships: List[str] = None,
        use_all_constants: bool = True
    ):
        """
        Initialize the prompt generator.
        
        Args:
            focus_entities: Specific entity types to focus on
            focus_relationships: Specific relationship types to focus on
            use_all_constants: Whether to use all available constants
        """
        if use_all_constants:
            self.focus_entities = list(get_all_entity_types())
            self.focus_relationships = list(get_all_relationship_types())
        else:
            self.focus_entities = focus_entities or ["Person", "Project", "Company"]
            self.focus_relationships = focus_relationships or ["involved_in", "mentions"]
    
    def generate_relationship_extraction_prompt(self, content: str) -> str:
        """
        Generate a dynamic prompt for relationship extraction.
        
        Args:
            content: Document content to extract from
            
        Returns:
            Complete prompt for LLM
        """
        # Group entities by category for better organization
        entity_groups = self._group_entities_by_category()
        relationship_groups = self._group_relationships_by_category()
        
        # Generate entity types section
        entity_section = self._format_entity_types(entity_groups)
        
        # Generate relationship types section
        relationship_section = self._format_relationship_types(relationship_groups)
        
        # Generate examples based on common patterns
        examples_section = self._generate_examples()
        
        prompt = f"""
Please extract entity relationships from this enterprise document:

{content}

{entity_section}

{relationship_section}

EXTRACTION GUIDELINES:
1. Focus on business-relevant entities and relationships
2. Use exact entity names as they appear in the document
3. Use only the predefined relationship types listed above
4. Ensure subject and object are clear, specific entity names
5. Avoid generic terms - use specific names (e.g., "John Smith" not "the manager")
6. Extract relationships that are explicitly stated or clearly implied
7. Prioritize relationships between people, projects, organizations, and systems

{examples_section}

Extract all relevant relationships following this pattern.
"""
        return prompt
    
    def generate_entity_extraction_prompt(self, content: str) -> str:
        """
        Generate a dynamic prompt for entity-only extraction.
        
        Args:
            content: Document content to extract from
            
        Returns:
            Complete prompt for LLM
        """
        entity_groups = self._group_entities_by_category()
        entity_section = self._format_entity_types(entity_groups)
        
        prompt = f"""
Please extract all relevant entities from this enterprise document:

{content}

{entity_section}

EXTRACTION GUIDELINES:
1. Identify specific, named entities (not generic references)
2. Use the most specific entity type available
3. Include the exact name as it appears in the document
4. Provide a brief description if the entity's role/purpose is clear
5. Focus on business-relevant entities

EXAMPLES:
- Entity(name="John Smith", entity_type="Person", description="Project Manager")
- Entity(name="CRM System", entity_type="System", description="Customer relationship management platform")
- Entity(name="Q4 Marketing Campaign", entity_type="Campaign", description="Fourth quarter marketing initiative")

Extract all relevant entities following this pattern.
"""
        return prompt
    
    def generate_summarization_prompt(self, content: str, document_type: str = None) -> str:
        """
        Generate a dynamic prompt for document summarization.
        
        Args:
            content: Document content to summarize
            document_type: Optional document type for specialized prompts
            
        Returns:
            Complete prompt for LLM
        """
        base_prompt = f"""
Please analyze this enterprise document and provide a comprehensive summary:

{content}

Focus on extracting the main title, key points, document type, and important topics discussed.
Be concise but thorough. If the document type is unclear, make your best assessment based on content and structure.
"""
        
        if document_type:
            specialized_instruction = self._get_specialized_summarization_instruction(document_type)
            base_prompt += f"\n\nSpecialized Instructions for {document_type}:\n{specialized_instruction}"
        
        return base_prompt
    
    def _group_entities_by_category(self) -> Dict[str, List[str]]:
        """Group entity types by category for better organization."""
        groups = {
            "People & Roles": [],
            "Organizations": [],
            "Projects & Initiatives": [],
            "Systems & Technology": [],
            "Documents": [],
            "Other": []
        }
        
        # Categorize entities
        person_types = get_person_related_types()
        org_types = get_organizational_types()
        project_types = get_project_related_types()
        system_types = get_system_related_types()
        doc_types = get_document_related_types()
        
        for entity_type in self.focus_entities:
            if entity_type in person_types:
                groups["People & Roles"].append(entity_type)
            elif entity_type in org_types:
                groups["Organizations"].append(entity_type)
            elif entity_type in project_types:
                groups["Projects & Initiatives"].append(entity_type)
            elif entity_type in system_types:
                groups["Systems & Technology"].append(entity_type)
            elif entity_type in doc_types:
                groups["Documents"].append(entity_type)
            else:
                groups["Other"].append(entity_type)
        
        # Remove empty groups
        return {k: v for k, v in groups.items() if v}
    
    def _group_relationships_by_category(self) -> Dict[str, List[str]]:
        """Group relationship types by category."""
        groups = {
            "Project Relationships": [],
            "Organizational Relationships": [],
            "Document Relationships": [],
            "Other Relationships": []
        }
        
        project_rels = get_project_relationships()
        org_rels = get_organizational_relationships()
        doc_rels = get_document_relationships()
        
        for rel_type in self.focus_relationships:
            if rel_type in project_rels:
                groups["Project Relationships"].append(rel_type)
            elif rel_type in org_rels:
                groups["Organizational Relationships"].append(rel_type)
            elif rel_type in doc_rels:
                groups["Document Relationships"].append(rel_type)
            else:
                groups["Other Relationships"].append(rel_type)
        
        # Remove empty groups
        return {k: v for k, v in groups.items() if v}
    
    def _format_entity_types(self, entity_groups: Dict[str, List[str]]) -> str:
        """Format entity types for the prompt."""
        sections = ["ENTITY TYPES to identify:"]
        
        for category, entities in entity_groups.items():
            if entities:
                sections.append(f"\n{category}:")
                sections.append(", ".join(entities))
        
        return "\n".join(sections)
    
    def _format_relationship_types(self, relationship_groups: Dict[str, List[str]]) -> str:
        """Format relationship types for the prompt."""
        sections = ["RELATIONSHIP TYPES to extract:"]
        
        for category, relationships in relationship_groups.items():
            if relationships:
                sections.append(f"\n{category}:")
                sections.append(", ".join(relationships))
        
        return "\n".join(sections)
    
    def _generate_examples(self) -> str:
        """Generate examples based on common patterns."""
        patterns = get_common_entity_relationship_patterns()
        
        examples = ["EXAMPLES:"]
        
        # Filter patterns to only include those in our focus types
        relevant_patterns = [
            pattern for pattern in patterns
            if pattern[1] in self.focus_relationships
        ]
        
        # Take first 5 relevant patterns
        for i, (subject_type, rel_type, object_type) in enumerate(relevant_patterns[:5]):
            # Generate example names based on types
            subject_example = self._get_example_name(subject_type)
            object_example = self._get_example_name(object_type)
            
            examples.append(f'- "{subject_example}" -> "{rel_type}" -> "{object_example}"')
        
        return "\n".join(examples)
    
    def _get_example_name(self, entity_type: str) -> str:
        """Get example name for an entity type."""
        examples = {
            "Person": "John Smith",
            "Employee": "Sarah Johnson",
            "Manager": "Mike Chen",
            "Executive": "Jennifer Walsh",
            "Project": "Project Alpha",
            "Initiative": "Digital Transformation Initiative",
            "Program": "Customer Experience Program",
            "Company": "TechCorp",
            "Department": "Engineering Department",
            "Team": "Marketing Team",
            "System": "CRM System",
            "Application": "Customer Portal",
            "Platform": "ERP Platform",
            "Database": "Customer Database",
            "Document": "Project Report",
            "Report": "Status Report",
            "Proposal": "Budget Proposal"
        }
        
        return examples.get(entity_type, f"Example {entity_type}")
    
    def _get_specialized_summarization_instruction(self, document_type: str) -> str:
        """Get specialized instruction for document type."""
        instructions = {
            "report": "Focus on extracting the executive summary, key findings, recommendations, and methodology.",
            "proposal": "Focus on the proposed solution, objectives, timeline, budget considerations, and key stakeholders.",
            "email": "Focus on the main purpose, action items, decisions made, and people involved.",
            "contract": "Focus on the parties involved, main terms, obligations, deliverables, and timeline.",
            "policy": "Focus on the policy objectives, scope, key requirements, compliance aspects, and affected stakeholders.",
            "procedure": "Focus on the process steps, roles and responsibilities, inputs/outputs, and quality requirements.",
            "meeting_notes": "Focus on attendees, key decisions, action items, discussion topics, and next steps."
        }
        
        return instructions.get(document_type.lower(), "Analyze the document structure and content to identify key information.")
    
    def get_schema_description(self) -> str:
        """Get the schema description for structured output."""
        return """
[
    {
        "subject": "string - the source entity (specific name)",
        "predicate": "string - the relationship type",
        "object": "string - the target entity (specific name)",
        "confidence_score": "float - confidence in this relationship (0.0-1.0)",
        "context": "string - brief context where this was found",
        "source_sentence": "string - the sentence where this relationship was mentioned"
    }
]
"""
    
    def update_focus_types(
        self,
        focus_entities: List[str] = None,
        focus_relationships: List[str] = None
    ):
        """Update the focus types for prompt generation."""
        if focus_entities is not None:
            self.focus_entities = focus_entities
        if focus_relationships is not None:
            self.focus_relationships = focus_relationships


# Factory functions for common configurations
def create_full_prompt_generator() -> PromptGenerator:
    """Create a prompt generator with all available constants."""
    return PromptGenerator(use_all_constants=True)


def create_basic_prompt_generator() -> PromptGenerator:
    """Create a prompt generator with basic entity and relationship types."""
    basic_entities = ["Person", "Project", "Company", "Department", "System"]
    basic_relationships = ["involved_in", "mentions", "works_for", "manages", "reports_to"]
    
    return PromptGenerator(
        focus_entities=basic_entities,
        focus_relationships=basic_relationships,
        use_all_constants=False
    )


def create_project_focused_generator() -> PromptGenerator:
    """Create a prompt generator focused on project-related extraction."""
    project_entities = list(get_person_related_types()) + list(get_project_related_types())
    project_relationships = list(get_project_relationships())
    
    return PromptGenerator(
        focus_entities=project_entities,
        focus_relationships=project_relationships,
        use_all_constants=False
    )
