"""
Example usage of the Enterprise KG Pipeline State Management system.

This script demonstrates how to use the pipeline state management module
to process organization data through the complete enterprise KG pipeline.
"""

import os
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main example function."""
    print("🚀 Enterprise KG Pipeline State Management Example")
    print("=" * 60)
    
    # Step 1: Setup configuration
    print("\n1. Setting up configuration...")
    config = setup_configuration()
    
    # Step 2: Initialize pipeline orchestrator
    print("\n2. Initializing pipeline orchestrator...")
    orchestrator = initialize_orchestrator(config)
    
    # Step 3: Process organization data
    print("\n3. Processing organization data...")
    result = process_rapid_innovation(orchestrator, config)
    
    # Step 4: Monitor status
    print("\n4. Monitoring pipeline status...")
    monitor_pipeline_status(orchestrator, result)
    
    # Step 5: Generate reports
    print("\n5. Generating status reports...")
    generate_reports(orchestrator)
    
    print("\n✅ Example completed successfully!")
    print("\nNext steps:")
    print("- Check your Neo4j database for the created knowledge graph")
    print("- Review the PostgreSQL database for pipeline state tracking")
    print("- Use the status tracker to monitor ongoing processing")


def setup_configuration() -> Dict[str, Any]:
    """Setup configuration for the pipeline."""
    config = {
        "postgres": {
            "host": os.getenv("POSTGRES_HOST", "localhost"),
            "port": int(os.getenv("POSTGRES_PORT", "5432")),
            "database": os.getenv("POSTGRES_DB", "enterprise_kg"),
            "username": os.getenv("POSTGRES_USER", "postgres"),
            "password": os.getenv("POSTGRES_PASSWORD", "password")
        },
        "neo4j": {
            "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
            "user": os.getenv("NEO4J_USER", "neo4j"),
            "password": os.getenv("NEO4J_PASSWORD", "password"),
            "database": os.getenv("NEO4J_DATABASE", None)
        },
        "llm": {
            "provider": "requesty",  # Using verified model
            "model": "claude-3-5-sonnet-20241022",
            "api_key": os.getenv("REQUESTY_API_KEY", "your-requesty-api-key")
        },
        "pinecone": {
            "api_key": os.getenv("PINECONE_API_KEY", "your-pinecone-api-key"),
            "environment": os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp"),
            "index_name": "enterprise-kg-rapid"
        }
    }
    
    print(f"✓ PostgreSQL: {config['postgres']['host']}:{config['postgres']['port']}")
    print(f"✓ Neo4j: {config['neo4j']['uri']}")
    print(f"✓ LLM: {config['llm']['provider']} - {config['llm']['model']}")
    print(f"✓ Pinecone: {config['pinecone']['environment']}")
    
    return config


def initialize_orchestrator(config: Dict[str, Any]):
    """Initialize the pipeline orchestrator."""
    from pipeline_state import PipelineOrchestrator
    
    try:
        orchestrator = PipelineOrchestrator(
            postgres_config=config["postgres"],
            max_workers=2,  # Conservative for demo
            enable_parallel_processing=True
        )
        
        print("✓ Pipeline orchestrator initialized")
        print("✓ Database tables created/verified")
        print("✓ Default pipeline stages configured")
        
        return orchestrator
        
    except Exception as e:
        logger.error(f"Failed to initialize orchestrator: {e}")
        raise


def process_rapid_innovation(orchestrator, config: Dict[str, Any]) -> Dict[str, Any]:
    """Process Rapid Innovation organization data."""
    print("\nProcessing Rapid Innovation organization...")
    
    # Define data sources
    data_sources = [
        {
            "type": "files",
            "name": "Document Repository",
            "config": {
                "path": "documents",  # Relative to enterprise_kg directory
                "include_patterns": ["*.md", "*.txt", "*.pdf"],
                "exclude_patterns": [".*", "__pycache__"]
            }
        }
        # Note: Slack and Jira integrations are placeholders for now
        # {
        #     "type": "slack",
        #     "name": "Rapid Innovation Slack",
        #     "config": {"workspace": "rapid-innovation"},
        #     "credentials": {"token": "your-slack-token"}
        # },
        # {
        #     "type": "jira",
        #     "name": "Rapid Innovation Jira",
        #     "config": {"url": "https://rapid.atlassian.net"},
        #     "credentials": {"username": "user", "token": "token"}
        # }
    ]
    
    try:
        # Process organization
        result = orchestrator.process_organization(
            org_id="rapid_innovation",
            org_name="Rapid Innovation",
            data_sources=data_sources,
            neo4j_config=config["neo4j"],
            pinecone_config=config["pinecone"],
            llm_config=config["llm"]
        )
        
        if result.get("success", False):
            print(f"✓ Organization processing completed")
            print(f"  - Total jobs: {result['total_jobs']}")
            print(f"  - Successful: {result['successful_jobs']}")
            print(f"  - Failed: {result['failed_jobs']}")
        else:
            print(f"✗ Organization processing failed: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to process organization: {e}")
        return {"success": False, "error": str(e)}


def monitor_pipeline_status(orchestrator, processing_result: Dict[str, Any]):
    """Monitor pipeline status and show detailed information."""
    from pipeline_state import StatusTracker
    
    status_tracker = StatusTracker(orchestrator.db_manager)
    
    print("\n📊 Pipeline Status Overview:")
    print("-" * 40)
    
    # System health
    health = status_tracker.get_system_health()
    print(f"System Health: {health['status'].upper()}")
    if health.get('issues'):
        for issue in health['issues']:
            print(f"  ⚠️  {issue}")
    
    # Organization dashboard
    org_dashboard = status_tracker.get_organization_dashboard("rapid_innovation")
    if "error" not in org_dashboard:
        file_stats = org_dashboard["file_statistics"]
        job_stats = org_dashboard["job_statistics"]
        
        print(f"\n📁 File Statistics:")
        print(f"  - Total files: {file_stats['total_files']}")
        print(f"  - Total size: {file_stats['total_size']:,} bytes")
        print(f"  - By status: {file_stats['by_status']}")
        
        print(f"\n⚙️  Job Statistics:")
        print(f"  - Total jobs: {job_stats['total_jobs']}")
        print(f"  - Success rate: {job_stats['success_rate']:.1f}%")
        print(f"  - By status: {job_stats['by_status']}")
        
        # Show recent errors if any
        if org_dashboard["recent_errors"]:
            print(f"\n🚨 Recent Errors ({len(org_dashboard['recent_errors'])}):")
            for error in org_dashboard["recent_errors"][:3]:
                print(f"  - {error['error_type']}: {error['error_message'][:80]}...")
    
    # Show job details for failed jobs
    if processing_result.get("failed_jobs", 0) > 0:
        print(f"\n🔍 Failed Job Details:")
        job_results = processing_result.get("job_results", {})
        for job_id, job_result in job_results.items():
            if not job_result.get("success", False):
                job_timeline = status_tracker.get_job_timeline(job_id)
                if "error" not in job_timeline:
                    print(f"  Job {job_id}:")
                    print(f"    Error: {job_result.get('error', 'Unknown')}")
                    print(f"    Failed stage: {job_result.get('failed_stage', 'Unknown')}")


def generate_reports(orchestrator):
    """Generate and display status reports."""
    from pipeline_state import StatusTracker
    
    status_tracker = StatusTracker(orchestrator.db_manager)
    
    print("\n📋 Status Reports:")
    print("=" * 50)
    
    # System-wide report
    print("\n🌐 System Report:")
    system_report = status_tracker.generate_status_report()
    print(system_report)
    
    # Organization-specific report
    print("\n🏢 Rapid Innovation Report:")
    org_report = status_tracker.generate_status_report("rapid_innovation")
    print(org_report)
    
    # Performance metrics
    print("\n📈 Performance Metrics (Last 7 days):")
    metrics = status_tracker.get_performance_metrics(days=7)
    print(f"  - Total jobs: {metrics.total_jobs}")
    print(f"  - Success rate: {metrics.success_rate:.1f}%")
    print(f"  - Average processing time: {metrics.average_processing_time:.1f}s")
    print(f"  - Currently running: {metrics.running_jobs}")
    print(f"  - Pending: {metrics.pending_jobs}")


def demonstrate_retry_functionality(orchestrator):
    """Demonstrate retry functionality for failed jobs."""
    print("\n🔄 Demonstrating Retry Functionality:")
    print("-" * 40)
    
    # Retry failed jobs
    retry_result = orchestrator.retry_failed_jobs("rapid_innovation")
    
    if retry_result.get("success", False):
        print(f"✓ Retried {retry_result['retried_jobs']} failed jobs")
        if retry_result["retried_jobs"] == 0:
            print("  No failed jobs to retry")
    else:
        print(f"✗ Retry failed: {retry_result.get('error', 'Unknown error')}")


def demonstrate_status_monitoring():
    """Demonstrate real-time status monitoring."""
    print("\n📡 Real-time Status Monitoring:")
    print("-" * 40)
    print("In a production environment, you would:")
    print("1. Set up periodic status checks")
    print("2. Configure alerts for failed jobs")
    print("3. Monitor system health metrics")
    print("4. Generate automated reports")
    print("5. Implement dashboard visualization")
    
    print("\nExample monitoring code:")
    print("""
    import time
    from pipeline_state import StatusTracker
    
    def monitor_pipeline():
        status_tracker = StatusTracker(db_manager)
        
        while True:
            health = status_tracker.get_system_health()
            if health['status'] != 'healthy':
                send_alert(health)
            
            time.sleep(60)  # Check every minute
    """)


if __name__ == "__main__":
    try:
        main()
        
        # Optional: Demonstrate additional features
        print("\n" + "=" * 60)
        print("🔧 Additional Features Demo")
        print("=" * 60)
        
        # Re-initialize for additional demos
        config = setup_configuration()
        orchestrator = initialize_orchestrator(config)
        
        demonstrate_retry_functionality(orchestrator)
        demonstrate_status_monitoring()
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Example interrupted by user")
    except Exception as e:
        logger.error(f"Example failed: {e}")
        print(f"\n❌ Example failed: {e}")
        print("\nTroubleshooting:")
        print("1. Ensure PostgreSQL is running and accessible")
        print("2. Ensure Neo4j is running and accessible")
        print("3. Check your environment variables")
        print("4. Verify the documents directory exists")
        print("5. Check the logs for detailed error information")
