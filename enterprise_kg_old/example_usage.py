"""
Example usage of Enterprise KG

This script demonstrates how to use the Enterprise KG system
for extracting entities and relationships from enterprise documents.
"""

import os
import cocoindex
from enterprise_kg import create_enterprise_flow
from enterprise_kg.utils import setup_environment, print_setup_summary
from enterprise_kg.core.extractor import create_basic_extractor
from enterprise_kg.core.summarizer import create_default_summarizer
from enterprise_kg.storage.neo4j_client import create_default_neo4j_client
from enterprise_kg.storage.pinecone_client import create_default_pinecone_client


def main():
    """Main example function."""
    print("=== Enterprise KG Example Usage ===")
    print()
    
    # Step 1: Set up environment
    print("1. Setting up environment...")
    setup_info = setup_environment(
        create_sample_docs=True,
        sample_docs_dir="documents"
    )
    print_setup_summary(setup_info)
    
    # Step 2: Configure connections (using environment variables or defaults)
    print("\n2. Configuring database connections...")
    
    # Neo4j configuration
    neo4j_config = {
        "uri": os.getenv("NEO4J_URI", "bolt://localhost:7687"),
        "user": os.getenv("NEO4J_USER", "neo4j"),
        "password": os.getenv("NEO4J_PASSWORD", "cocoindex"),
        "database": os.getenv("NEO4J_DATABASE", None)
    }
    
    # Pinecone configuration (optional)
    pinecone_config = None
    if os.getenv("PINECONE_API_KEY"):
        pinecone_config = {
            "api_key": os.getenv("PINECONE_API_KEY"),
            "environment": os.getenv("PINECONE_ENVIRONMENT", "us-west1-gcp"),
            "index_name": "enterprise-kg",
            "dimension": 1536
        }
    
    # LLM configuration
    llm_config = {
        "api_type": cocoindex.LlmApiType.OPENAI,
        "model": "gpt-4o"
    }
    
    # Check if OpenAI API key is available
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  Warning: OPENAI_API_KEY not set. You may want to use a different LLM provider.")
        print("   Available options: OLLAMA, ANTHROPIC, GEMINI")
        print("   Example: Set ANTHROPIC_API_KEY and use cocoindex.LlmApiType.ANTHROPIC")
        
        # Fallback to Ollama for local testing
        llm_config = {
            "api_type": cocoindex.LlmApiType.OLLAMA,
            "model": "llama3.2"
        }
        print("   Falling back to Ollama with llama3.2 model")
    
    print(f"✓ LLM Provider: {llm_config['api_type']}")
    print(f"✓ Model: {llm_config['model']}")
    print(f"✓ Neo4j: {neo4j_config['uri']}")
    if pinecone_config:
        print(f"✓ Pinecone: {pinecone_config['environment']}")
    else:
        print("✗ Pinecone: Not configured (optional)")
    
    # Step 3: Create the enterprise flow
    print("\n3. Creating enterprise knowledge graph flow...")
    
    try:
        # Create flow with basic extraction (your initial requirements)
        flow = create_enterprise_flow(
            source_path="documents",
            flow_name="BasicEnterpriseKG",
            llm_config=llm_config,
            pinecone_config=pinecone_config,
            neo4j_config=neo4j_config,
            use_basic_extraction=True  # Focus on Person-involved_in-Project relationships
        )
        
        print("✓ Enterprise KG flow created successfully")
        
        # Create the CocoIndex flow
        cocoindex_flow = flow.create_flow(
            included_patterns=["*.md", "*.txt"],
            excluded_patterns=["**/.*"]
        )
        
        print("✓ CocoIndex flow configured")
        
    except Exception as e:
        print(f"✗ Error creating flow: {e}")
        return
    
    # Step 4: Run the flow (this would typically be done via CocoIndex CLI)
    print("\n4. Flow ready for execution!")
    print("\nTo run the flow, use one of these methods:")
    print()
    print("Method 1 - CocoIndex CLI:")
    print("  cocoindex run BasicEnterpriseKG")
    print()
    print("Method 2 - Python script:")
    print("  # The flow function is ready to be executed")
    print("  # This would integrate with CocoIndex's execution system")
    print()
    
    # Step 5: Show what the flow will extract
    print("5. Expected extraction results:")
    print()
    print("The flow will extract relationships like:")
    print("  • Sarah Johnson involved_in Project Alpha")
    print("  • Mike Chen involved_in Project Alpha") 
    print("  • Project Alpha mentions CRM System")
    print("  • Engineering Department part_of Company")
    print("  • Alex Thompson manages Backend Development Team")
    print()
    
    # Step 6: Show storage locations
    print("6. Data will be stored in:")
    print("  • Document summaries → PostgreSQL (document_summaries table)")
    print("  • Entity relationships → Neo4j (Entity nodes with RELATIONSHIP edges)")
    if pinecone_config:
        print("  • Document embeddings → Pinecone (for semantic search)")
    print("  • Processing metadata → PostgreSQL (for tracking)")
    print()
    
    print("=== Example Complete ===")
    print()
    print("Next steps:")
    print("1. Ensure your databases are running (Neo4j, PostgreSQL)")
    print("2. Set up your LLM API keys")
    print("3. Run the flow using CocoIndex CLI")
    print("4. Query the results from Neo4j and PostgreSQL")


def demonstrate_direct_extraction():
    """
    Demonstrate direct extraction without CocoIndex flow.
    
    This shows how to use the extraction components directly.
    """
    print("\n=== Direct Extraction Demo ===")
    
    # Sample text
    sample_text = """
    Project Alpha Status Update
    
    Sarah Johnson, our Senior Project Manager, is leading Project Alpha. 
    The development team includes Mike Chen (Lead Developer) and Lisa Rodriguez (UX Designer).
    
    The project involves building a new CRM System that will integrate with our ERP Platform.
    David Kim from the Data Analytics team is also involved in the project.
    
    The Engineering Department, led by Jennifer Walsh, is collaborating with the Sales Team
    on this initiative.
    """
    
    try:
        # Create extractor with basic settings
        extractor = create_basic_extractor(
            api_type=cocoindex.LlmApiType.OPENAI,
            model="gpt-4o"
        )
        
        print("Sample text:")
        print(sample_text)
        print()
        print("This would extract relationships like:")
        print("• Sarah Johnson involved_in Project Alpha")
        print("• Mike Chen involved_in Project Alpha")
        print("• Lisa Rodriguez involved_in Project Alpha")
        print("• David Kim involved_in Project Alpha")
        print("• Project Alpha mentions CRM System")
        print("• Jennifer Walsh manages Engineering Department")
        print()
        print("Note: Actual extraction requires LLM API access")
        
    except Exception as e:
        print(f"Error in direct extraction demo: {e}")


def show_configuration_options():
    """Show different configuration options for the system."""
    print("\n=== Configuration Options ===")
    print()
    
    print("1. LLM Providers:")
    print("   • OpenAI (gpt-4o, gpt-3.5-turbo)")
    print("   • Anthropic (claude-3-5-sonnet-latest)")
    print("   • Google Gemini (gemini-2.0-flash)")
    print("   • Ollama (llama3.2, mistral, etc.) - Local")
    print()
    
    print("2. Entity Types (Basic):")
    print("   • Person, Employee, Manager, Executive")
    print("   • Project, Initiative, Program")
    print("   • Company, Department, Team")
    print("   • System, Application, Platform")
    print()
    
    print("3. Relationship Types (Basic):")
    print("   • involved_in, mentions")
    print("   • works_for, manages, reports_to")
    print("   • member_of, part_of")
    print("   • uses, integrates_with")
    print()
    
    print("4. Storage Options:")
    print("   • Neo4j: Graph database for entities and relationships")
    print("   • Pinecone: Vector database for semantic search")
    print("   • PostgreSQL: Relational database for summaries and metadata")
    print()
    
    print("5. Processing Options:")
    print("   • Document chunking: Split large documents")
    print("   • Embedding generation: Create vector representations")
    print("   • Batch processing: Process multiple documents")
    print("   • Incremental updates: Process only new/changed documents")


if __name__ == "__main__":
    main()
    demonstrate_direct_extraction()
    show_configuration_options()
