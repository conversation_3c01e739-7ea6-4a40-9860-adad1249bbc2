#!/usr/bin/env python3
"""
Test Hybrid Search Strategies and Edge Cases

This script comprehensively tests all hybrid search strategies and their
edge cases to ensure robust functionality.
"""

import os
import sys
import time
from typing import List, Dict, Set, Any
from dotenv import load_dotenv

# Add the enterprise_kg_minimal to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'enterprise_kg_minimal'))

load_dotenv()

def test_all_search_strategies():
    """Test all available search strategies."""
    print("\n🔍 Testing All Search Strategies")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Get real chunks for testing
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 5"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:3] if result else ["test_chunk"]
        
        strategies = [
            SearchStrategy.ENTITY_CENTRIC,
            SearchStrategy.RELATIONSHIP_CENTRIC,
            SearchStrategy.CHUNK_EXPANSION,
            SearchStrategy.HIERARCHICAL,
            SearchStrategy.HYBRID
        ]
        
        strategy_results = {}
        
        for strategy in strategies:
            print(f"\n🧪 Testing {strategy.value} strategy...")
            
            try:
                start_time = time.time()
                
                result = search_engine.search(
                    chunk_indices=real_chunks,
                    query_text=f"test {strategy.value} strategy",
                    strategy=strategy,
                    max_results=25,
                    expansion_depth=2
                )
                
                end_time = time.time()
                
                strategy_results[strategy.value] = {
                    'success': True,
                    'total_results': result.total_results,
                    'entities': len(result.graph_context.entities),
                    'relationships': len(result.graph_context.relationships),
                    'processing_time_ms': result.processing_time_ms,
                    'actual_time_ms': (end_time - start_time) * 1000,
                    'coverage_score': result.coverage_score,
                    'coherence_score': result.coherence_score,
                    'relevance_score': result.relevance_score,
                    'debug_info': result.debug_info
                }
                
                print(f"   ✅ Success: {result.total_results} results in {result.processing_time_ms:.2f}ms")
                print(f"   📊 Entities: {len(result.graph_context.entities)}, "
                      f"Relationships: {len(result.graph_context.relationships)}")
                print(f"   📈 Scores: Coverage={result.coverage_score:.3f}, "
                      f"Coherence={result.coherence_score:.3f}, "
                      f"Relevance={result.relevance_score:.3f}")
                
            except Exception as e:
                strategy_results[strategy.value] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"   ❌ Failed: {e}")
        
        # Compare strategy performance
        print(f"\n📊 Strategy Performance Comparison:")
        successful_strategies = {k: v for k, v in strategy_results.items() if v.get('success')}
        
        if successful_strategies:
            # Sort by total results
            sorted_by_results = sorted(successful_strategies.items(), 
                                     key=lambda x: x[1]['total_results'], reverse=True)
            
            print(f"   By total results:")
            for strategy, data in sorted_by_results:
                print(f"      {strategy}: {data['total_results']} results")
            
            # Sort by processing time
            sorted_by_time = sorted(successful_strategies.items(), 
                                  key=lambda x: x[1]['processing_time_ms'])
            
            print(f"   By processing time:")
            for strategy, data in sorted_by_time:
                print(f"      {strategy}: {data['processing_time_ms']:.2f}ms")
        
        search_engine.neo4j_client.close()
        return len([r for r in strategy_results.values() if r.get('success')]) == len(strategies)
        
    except Exception as e:
        print(f"❌ Strategy testing failed: {e}")
        return False

def test_strategy_specific_parameters():
    """Test strategy-specific parameters and filters."""
    print("\n⚙️ Testing Strategy-Specific Parameters")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Get real chunks and available types
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 3"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:2] if result else ["test_chunk"]
        
        # Get available entity and relationship types
        available_types = search_engine.get_available_types()
        entity_types = list(available_types['entity_types'])[:5]  # Use first 5
        relationship_types = list(available_types['relationship_types'])[:5]  # Use first 5
        
        # Test different parameter combinations
        test_cases = [
            {
                'name': 'Entity type filtering',
                'params': {
                    'entity_types': set(entity_types[:2]) if entity_types else None,
                    'strategy': SearchStrategy.ENTITY_CENTRIC
                }
            },
            {
                'name': 'Relationship type filtering',
                'params': {
                    'relationship_types': set(relationship_types[:2]) if relationship_types else None,
                    'strategy': SearchStrategy.RELATIONSHIP_CENTRIC
                }
            },
            {
                'name': 'High confidence threshold',
                'params': {
                    'min_confidence_score': 0.8,
                    'strategy': SearchStrategy.CHUNK_EXPANSION
                }
            },
            {
                'name': 'Low confidence threshold',
                'params': {
                    'min_confidence_score': 0.1,
                    'strategy': SearchStrategy.CHUNK_EXPANSION
                }
            },
            {
                'name': 'Boost high importance',
                'params': {
                    'boost_high_importance': True,
                    'strategy': SearchStrategy.ENTITY_CENTRIC
                }
            },
            {
                'name': 'Boost recent entities',
                'params': {
                    'boost_recent_entities': True,
                    'strategy': SearchStrategy.ENTITY_CENTRIC
                }
            },
            {
                'name': 'Include context flags',
                'params': {
                    'include_chunk_context': True,
                    'include_file_context': True,
                    'strategy': SearchStrategy.HIERARCHICAL
                }
            },
            {
                'name': 'Exclude context flags',
                'params': {
                    'include_chunk_context': False,
                    'include_file_context': False,
                    'strategy': SearchStrategy.HIERARCHICAL
                }
            }
        ]
        
        for test_case in test_cases:
            print(f"\n🧪 Testing: {test_case['name']}")
            
            try:
                base_params = {
                    'chunk_indices': real_chunks,
                    'query_text': f"test {test_case['name']}",
                    'max_results': 15,
                    'expansion_depth': 1
                }
                
                # Merge with test-specific parameters
                test_params = {**base_params, **test_case['params']}
                
                result = search_engine.search(**test_params)
                
                print(f"   ✅ Success: {result.total_results} results")
                print(f"   📊 Entities: {len(result.graph_context.entities)}, "
                      f"Relationships: {len(result.graph_context.relationships)}")
                print(f"   ⏱️  Processing time: {result.processing_time_ms:.2f}ms")
                
                # Check if filters were applied correctly
                if 'entity_types' in test_case['params'] and test_case['params']['entity_types']:
                    filtered_types = test_case['params']['entity_types']
                    found_types = set(e.entity_type for e in result.graph_context.entities)
                    unexpected_types = found_types - filtered_types
                    
                    if unexpected_types:
                        print(f"   ⚠️  Found unexpected entity types: {unexpected_types}")
                    else:
                        print(f"   ✅ Entity type filtering worked correctly")
                
                if 'relationship_types' in test_case['params'] and test_case['params']['relationship_types']:
                    filtered_rel_types = test_case['params']['relationship_types']
                    found_rel_types = set(r.relationship_type for r in result.graph_context.relationships)
                    unexpected_rel_types = found_rel_types - filtered_rel_types
                    
                    if unexpected_rel_types:
                        print(f"   ⚠️  Found unexpected relationship types: {unexpected_rel_types}")
                    else:
                        print(f"   ✅ Relationship type filtering worked correctly")
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Parameter testing failed: {e}")
        return False

def test_search_with_different_depths():
    """Test search behavior with different expansion depths."""
    print("\n📏 Testing Different Expansion Depths")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Get real chunks
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 3"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:2] if result else ["test_chunk"]
        
        depths = [0, 1, 2, 3, 4]
        depth_results = {}
        
        for depth in depths:
            print(f"\n🧪 Testing expansion depth: {depth}")
            
            try:
                start_time = time.time()
                
                result = search_engine.search(
                    chunk_indices=real_chunks,
                    query_text=f"test depth {depth}",
                    strategy=SearchStrategy.CHUNK_EXPANSION,
                    max_results=30,
                    expansion_depth=depth
                )
                
                end_time = time.time()
                
                depth_results[depth] = {
                    'total_results': result.total_results,
                    'entities': len(result.graph_context.entities),
                    'relationships': len(result.graph_context.relationships),
                    'processing_time_ms': result.processing_time_ms,
                    'actual_time_ms': (end_time - start_time) * 1000,
                    'max_depth_reached': result.graph_context.max_depth_reached
                }
                
                print(f"   ✅ Results: {result.total_results} total")
                print(f"   📊 Entities: {len(result.graph_context.entities)}, "
                      f"Relationships: {len(result.graph_context.relationships)}")
                print(f"   📏 Max depth reached: {result.graph_context.max_depth_reached}")
                print(f"   ⏱️  Processing time: {result.processing_time_ms:.2f}ms")
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
                depth_results[depth] = {'error': str(e)}
        
        # Analyze depth progression
        print(f"\n📊 Depth Progression Analysis:")
        
        successful_depths = {k: v for k, v in depth_results.items() if 'error' not in v}
        
        if len(successful_depths) > 1:
            print(f"   Results by depth:")
            for depth in sorted(successful_depths.keys()):
                data = successful_depths[depth]
                print(f"      Depth {depth}: {data['total_results']} results, "
                      f"{data['processing_time_ms']:.2f}ms")
            
            # Check if results increase with depth (as expected)
            result_counts = [successful_depths[d]['total_results'] for d in sorted(successful_depths.keys())]
            processing_times = [successful_depths[d]['processing_time_ms'] for d in sorted(successful_depths.keys())]
            
            if result_counts == sorted(result_counts):
                print(f"   ✅ Results increase monotonically with depth")
            else:
                print(f"   ⚠️  Results don't increase monotonically: {result_counts}")
            
            # Check processing time trend
            if len(processing_times) > 1:
                time_increases = all(processing_times[i] <= processing_times[i+1] * 2 
                                   for i in range(len(processing_times)-1))
                if time_increases:
                    print(f"   ✅ Processing time increases reasonably with depth")
                else:
                    print(f"   ⚠️  Processing time increases may be excessive: {processing_times}")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Depth testing failed: {e}")
        return False

def test_result_aggregation():
    """Test result aggregation and deduplication."""
    print("\n🔄 Testing Result Aggregation")
    print("=" * 50)
    
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import create_hybrid_search_engine
        from enterprise_kg_minimal.search.search_schemas import SearchStrategy
        
        search_engine = create_hybrid_search_engine(
            neo4j_uri=os.getenv('NEO4J_URI', 'bolt://localhost:7687'),
            neo4j_user=os.getenv('NEO4J_USER', 'neo4j'),
            neo4j_password=os.getenv('NEO4J_PASSWORD', 'password')
        )
        
        # Get real chunks
        query = "MATCH (c:Chunk) RETURN c.id LIMIT 4"
        result = search_engine.neo4j_client.execute_query(query)
        real_chunks = [record['c.id'] for record in result][:3] if result else ["test_chunk"]
        
        # Test 1: Same search multiple times to check for duplicates
        print(f"\n🧪 Testing duplicate detection...")
        
        result = search_engine.search(
            chunk_indices=real_chunks,
            query_text="test aggregation",
            strategy=SearchStrategy.HYBRID,
            max_results=50,
            expansion_depth=2
        )
        
        # Check for duplicate entities
        entity_names = [e.name.lower() if e.name else "" for e in result.graph_context.entities]
        unique_entity_names = set(entity_names)
        
        if len(entity_names) == len(unique_entity_names):
            print(f"   ✅ No duplicate entities found ({len(entity_names)} entities)")
        else:
            duplicates = len(entity_names) - len(unique_entity_names)
            print(f"   ⚠️  Found {duplicates} potential duplicate entities")
        
        # Check for duplicate relationships
        rel_signatures = [(r.source_entity.lower() if r.source_entity else "",
                          r.relationship_type.lower() if r.relationship_type else "",
                          r.target_entity.lower() if r.target_entity else "")
                         for r in result.graph_context.relationships]
        unique_rel_signatures = set(rel_signatures)
        
        if len(rel_signatures) == len(unique_rel_signatures):
            print(f"   ✅ No duplicate relationships found ({len(rel_signatures)} relationships)")
        else:
            duplicates = len(rel_signatures) - len(unique_rel_signatures)
            print(f"   ⚠️  Found {duplicates} potential duplicate relationships")
        
        # Test 2: Overlapping chunk sets
        print(f"\n🧪 Testing overlapping chunk aggregation...")
        
        if len(real_chunks) >= 2:
            # Search with first two chunks
            result1 = search_engine.search(
                chunk_indices=real_chunks[:2],
                query_text="test overlap 1",
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=20,
                expansion_depth=1
            )
            
            # Search with last two chunks (overlapping)
            result2 = search_engine.search(
                chunk_indices=real_chunks[1:3] if len(real_chunks) >= 3 else real_chunks[1:2],
                query_text="test overlap 2",
                strategy=SearchStrategy.CHUNK_EXPANSION,
                max_results=20,
                expansion_depth=1
            )
            
            print(f"   📊 Result 1: {len(result1.graph_context.entities)} entities, "
                  f"{len(result1.graph_context.relationships)} relationships")
            print(f"   📊 Result 2: {len(result2.graph_context.entities)} entities, "
                  f"{len(result2.graph_context.relationships)} relationships")
            
            # Check for overlap in results
            entities1 = set(e.name.lower() if e.name else "" for e in result1.graph_context.entities)
            entities2 = set(e.name.lower() if e.name else "" for e in result2.graph_context.entities)
            entity_overlap = entities1.intersection(entities2)
            
            if entity_overlap:
                print(f"   📊 Entity overlap: {len(entity_overlap)} entities")
                print(f"   ✅ Overlapping chunks produce overlapping results (expected)")
            else:
                print(f"   ⚠️  No entity overlap found (may indicate poor connectivity)")
        
        # Test 3: Score consistency
        print(f"\n🧪 Testing score consistency...")
        
        if result.graph_context.entities:
            # Check if scores are properly normalized
            entity_scores = [e.relevance_score for e in result.graph_context.entities 
                           if e.relevance_score is not None]
            
            if entity_scores:
                max_score = max(entity_scores)
                min_score = min(entity_scores)
                
                if 0 <= min_score <= max_score <= 1:
                    print(f"   ✅ Entity scores properly normalized: [{min_score:.3f}, {max_score:.3f}]")
                else:
                    print(f"   ⚠️  Entity scores outside [0,1]: [{min_score:.3f}, {max_score:.3f}]")
        
        if result.graph_context.relationships:
            # Check relationship confidence scores
            rel_scores = [r.confidence_score for r in result.graph_context.relationships 
                         if r.confidence_score is not None]
            
            if rel_scores:
                max_score = max(rel_scores)
                min_score = min(rel_scores)
                
                if 0 <= min_score <= max_score <= 1:
                    print(f"   ✅ Relationship scores properly normalized: [{min_score:.3f}, {max_score:.3f}]")
                else:
                    print(f"   ⚠️  Relationship scores outside [0,1]: [{min_score:.3f}, {max_score:.3f}]")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Aggregation testing failed: {e}")
        return False

def main():
    """Main test function for hybrid search strategies."""
    print("🧪 Hybrid Search Strategies Test Suite")
    print("=" * 70)
    
    test_results = {}
    
    # Test 1: All search strategies
    print("\n" + "🔍 PHASE 1: Strategy Testing" + "\n" + "=" * 50)
    test_results['all_strategies'] = test_all_search_strategies()
    
    # Test 2: Strategy-specific parameters
    print("\n" + "⚙️ PHASE 2: Parameter Testing" + "\n" + "=" * 50)
    test_results['strategy_parameters'] = test_strategy_specific_parameters()
    
    # Test 3: Different expansion depths
    print("\n" + "📏 PHASE 3: Depth Testing" + "\n" + "=" * 50)
    test_results['expansion_depths'] = test_search_with_different_depths()
    
    # Test 4: Result aggregation
    print("\n" + "🔄 PHASE 4: Aggregation Testing" + "\n" + "=" * 50)
    test_results['result_aggregation'] = test_result_aggregation()
    
    # Final summary
    print("\n" + "📊 STRATEGY TEST SUMMARY" + "\n" + "=" * 70)
    
    passed_tests = sum(1 for result in test_results.values() if result is True)
    failed_tests = sum(1 for result in test_results.values() if result is False)
    total_tests = len(test_results)
    
    print(f"✅ Passed: {passed_tests}/{total_tests}")
    print(f"❌ Failed: {failed_tests}/{total_tests}")
    
    print(f"\n📋 Detailed Results:")
    for test_name, result in test_results.items():
        status = "✅ PASS" if result is True else "❌ FAIL"
        print(f"   {test_name}: {status}")
    
    if failed_tests == 0:
        print(f"\n🎉 ALL STRATEGY TESTS PASSED!")
    else:
        print(f"\n⚠️  {failed_tests} test(s) failed. Review strategy implementations.")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
