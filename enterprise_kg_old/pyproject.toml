[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "enterprise-kg"
version = "0.1.0"
description = "Enterprise Knowledge Graph system built on CocoIndex"
readme = "README.md"
authors = [
    {name = "Enterprise KG Team", email = "<EMAIL>"}
]
license = {text = "MIT"}
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Database :: Database Engines/Servers",
    "Topic :: Text Processing :: Linguistic",
]
keywords = ["knowledge-graph", "nlp", "entity-extraction", "enterprise", "cocoindex"]
requires-python = ">=3.9"

dependencies = [
    "cocoindex>=0.1.0",
    "dataclasses-json>=0.5.0",
    "pydantic>=2.0.0",
    "typing-extensions>=4.0.0",
]

[project.optional-dependencies]
neo4j = [
    "neo4j>=5.0.0",
]
pinecone = [
    "pinecone-client>=2.0.0",
]
openai = [
    "openai>=1.0.0",
]
anthropic = [
    "anthropic>=0.7.0",
]
all = [
    "neo4j>=5.0.0",
    "pinecone-client>=2.0.0", 
    "openai>=1.0.0",
    "anthropic>=0.7.0",
]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/your-org/enterprise-kg"
Documentation = "https://enterprise-kg.readthedocs.io"
Repository = "https://github.com/your-org/enterprise-kg.git"
"Bug Tracker" = "https://github.com/your-org/enterprise-kg/issues"

[project.scripts]
enterprise-kg = "enterprise_kg.cli:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["enterprise_kg*"]

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["enterprise_kg"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "cocoindex.*",
    "neo4j.*",
    "pinecone.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=enterprise_kg",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-fail-under=80",
]

[tool.coverage.run]
source = ["enterprise_kg"]
omit = [
    "*/tests/*",
    "*/test_*",
    "enterprise_kg/example_usage.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
