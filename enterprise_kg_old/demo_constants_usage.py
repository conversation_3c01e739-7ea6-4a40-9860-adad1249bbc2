"""
Demo: Enterprise KG with Full Constants Usage

This script demonstrates how the updated Enterprise KG system now uses
all the defined entity and relationship constants to generate comprehensive
subject-predicate-object triples from summarized text.
"""

import os
import sys
import logging
from typing import List, Dict, Any

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from enterprise_kg_processor import create_kg_processor
from prompt_generator import create_full_prompt_generator, create_basic_prompt_generator
from constants.entities import get_all_entity_types, get_person_related_types
from constants.relationships import get_all_relationship_types, get_organizational_relationships

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def demonstrate_constants_usage():
    """Demonstrate how constants are used in prompt generation."""
    
    print("=" * 80)
    print("ENTERPRISE KG CONSTANTS USAGE DEMONSTRATION")
    print("=" * 80)
    
    # Show available constants
    print("\n1. AVAILABLE ENTITY TYPES:")
    print("-" * 40)
    all_entities = get_all_entity_types()
    print(f"Total entity types: {len(all_entities)}")
    for i, entity in enumerate(sorted(all_entities), 1):
        print(f"  {i:2d}. {entity}")
    
    print("\n2. AVAILABLE RELATIONSHIP TYPES:")
    print("-" * 40)
    all_relationships = get_all_relationship_types()
    print(f"Total relationship types: {len(all_relationships)}")
    for i, rel in enumerate(sorted(all_relationships), 1):
        print(f"  {i:2d}. {rel}")
    
    # Show prompt generation differences
    print("\n3. PROMPT GENERATION COMPARISON:")
    print("-" * 40)
    
    sample_content = """
    John Smith is the project manager for the Digital Transformation Initiative. 
    He works for TechCorp and reports to Sarah Johnson, the VP of Engineering. 
    The project uses the new CRM System and integrates with the existing ERP Platform.
    The Marketing Department collaborates with the Engineering Team on this initiative.
    """
    
    # Basic prompt generator (limited constants)
    basic_generator = create_basic_prompt_generator()
    basic_prompt = basic_generator.generate_relationship_extraction_prompt(sample_content)
    
    print("\nBASIC PROMPT (Limited Constants):")
    print("Entity types:", len(basic_generator.focus_entities))
    print("Relationship types:", len(basic_generator.focus_relationships))
    print("Prompt length:", len(basic_prompt), "characters")
    
    # Full prompt generator (all constants)
    full_generator = create_full_prompt_generator()
    full_prompt = full_generator.generate_relationship_extraction_prompt(sample_content)
    
    print("\nFULL PROMPT (All Constants):")
    print("Entity types:", len(full_generator.focus_entities))
    print("Relationship types:", len(full_generator.focus_relationships))
    print("Prompt length:", len(full_prompt), "characters")
    
    print(f"\nImprovement: {len(full_generator.focus_entities) - len(basic_generator.focus_entities)} more entity types")
    print(f"Improvement: {len(full_generator.focus_relationships) - len(basic_generator.focus_relationships)} more relationship types")


def demonstrate_processor_configuration():
    """Demonstrate different processor configurations."""
    
    print("\n4. PROCESSOR CONFIGURATION OPTIONS:")
    print("-" * 40)
    
    # Configuration 1: Full constants (default)
    print("\nConfiguration 1: Full Constants (Recommended)")
    try:
        processor_full = create_kg_processor(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j", 
            neo4j_password="password",
            use_all_constants=True
        )
        print(f"✓ Entity types: {len(processor_full.focus_entities)}")
        print(f"✓ Relationship types: {len(processor_full.focus_relationships)}")
        print("✓ Uses comprehensive prompt generation")
        processor_full.close()
    except Exception as e:
        print(f"✗ Configuration failed: {e}")
    
    # Configuration 2: Basic constants
    print("\nConfiguration 2: Basic Constants")
    try:
        processor_basic = create_kg_processor(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="password", 
            use_all_constants=False
        )
        print(f"✓ Entity types: {len(processor_basic.focus_entities)}")
        print(f"✓ Relationship types: {len(processor_basic.focus_relationships)}")
        print("✓ Uses basic prompt generation")
        processor_basic.close()
    except Exception as e:
        print(f"✗ Configuration failed: {e}")


def show_sample_extraction_output():
    """Show what the extraction output would look like."""
    
    print("\n5. SAMPLE EXTRACTION OUTPUT:")
    print("-" * 40)
    
    sample_document = """
    Project Status Report - Q4 2024
    
    The Digital Transformation Initiative, led by John Smith, has made significant progress.
    Sarah Johnson, VP of Engineering, approved the budget increase for the CRM System upgrade.
    The Marketing Department is collaborating with the Engineering Team to define requirements.
    
    Key achievements:
    - ERP Platform integration completed
    - Customer Portal launched successfully  
    - Data Migration Tool deployed to Production Environment
    
    Next steps:
    - Security Audit scheduled for January 2025
    - Training Program for end users
    - Performance Monitoring implementation
    """
    
    print("Sample Document:")
    print(sample_document[:200] + "..." if len(sample_document) > 200 else sample_document)
    
    print("\nExpected Subject-Predicate-Object Extractions:")
    expected_extractions = [
        ("John Smith", "leads", "Digital Transformation Initiative"),
        ("John Smith", "works_for", "Engineering Team"),
        ("Sarah Johnson", "manages", "John Smith"),
        ("Sarah Johnson", "approved_by", "Budget"),
        ("Marketing Department", "collaborates_with", "Engineering Team"),
        ("CRM System", "integrates_with", "ERP Platform"),
        ("Customer Portal", "runs_on", "Production Environment"),
        ("Data Migration Tool", "deployed_to", "Production Environment"),
        ("Security Audit", "scheduled_for", "January 2025"),
        ("Training Program", "targets", "End Users")
    ]
    
    for i, (subject, predicate, obj) in enumerate(expected_extractions, 1):
        print(f"  {i:2d}. \"{subject}\" -> \"{predicate}\" -> \"{obj}\"")


def show_integration_benefits():
    """Show the benefits of the integrated approach."""
    
    print("\n6. INTEGRATION BENEFITS:")
    print("-" * 40)
    
    benefits = [
        "✓ Comprehensive entity recognition using all defined types",
        "✓ Rich relationship extraction with business-specific predicates", 
        "✓ Consistent prompt generation across all processing",
        "✓ Easy maintenance - add new types in constants files",
        "✓ Scalable for enterprise knowledge graphs",
        "✓ Subject-Predicate-Object triples follow defined schemas",
        "✓ Dynamic prompt adaptation based on document content",
        "✓ Integration with existing Pinecone vector storage"
    ]
    
    for benefit in benefits:
        print(f"  {benefit}")
    
    print("\nKey Improvement:")
    print("  Before: Hardcoded prompts with limited entity/relationship types")
    print("  After:  Dynamic prompts using comprehensive constant definitions")


if __name__ == "__main__":
    print("Starting Enterprise KG Constants Usage Demonstration...")
    
    try:
        demonstrate_constants_usage()
        demonstrate_processor_configuration()
        show_sample_extraction_output()
        show_integration_benefits()
        
        print("\n" + "=" * 80)
        print("DEMONSTRATION COMPLETE")
        print("=" * 80)
        print("\nThe Enterprise KG system now properly uses all defined constants")
        print("to generate comprehensive subject-predicate-object triples from text!")
        
    except Exception as e:
        logger.error(f"Demonstration failed: {e}")
        print(f"\nError: {e}")
        print("Note: This demo requires Neo4j connection for full functionality.")
