#!/usr/bin/env python3
"""
Simple validation script for the hybrid search engine
"""

def main():
    print("🔍 Validating Hybrid Search Engine")
    print("=" * 40)
    
    # Test 1: Basic imports
    print("1. Testing basic imports...")
    try:
        from enterprise_kg_minimal.search.search_schemas import SearchQuery, SearchStrategy
        print("   ✅ Search schemas imported")
    except Exception as e:
        print(f"   ❌ Search schemas failed: {e}")
        return False
    
    # Test 2: Constants
    print("2. Testing constants...")
    try:
        from enterprise_kg_minimal.constants.entities import get_all_entity_types
        entity_types = get_all_entity_types()
        print(f"   ✅ {len(entity_types)} entity types loaded")
    except Exception as e:
        print(f"   ❌ Constants failed: {e}")
        return False
    
    # Test 3: Search query creation
    print("3. Testing search query creation...")
    try:
        query = SearchQuery(
            chunk_indices=["test_chunk_1", "test_chunk_2"],
            strategy=SearchStrategy.HYBRID
        )
        print(f"   ✅ Query created with {len(query.chunk_indices)} chunks")
    except Exception as e:
        print(f"   ❌ Query creation failed: {e}")
        return False
    
    # Test 4: Search engine import
    print("4. Testing search engine import...")
    try:
        from enterprise_kg_minimal.search.hybrid_search_engine import HybridSearchEngine
        print("   ✅ HybridSearchEngine imported")
    except Exception as e:
        print(f"   ❌ Search engine import failed: {e}")
        return False
    
    print("\n🎉 All validation tests passed!")
    print("\n📋 Summary:")
    print("   ✅ Search schemas working")
    print("   ✅ Constants loading correctly")
    print("   ✅ Search queries can be created")
    print("   ✅ Search engine can be imported")
    print("\n🚀 The hybrid search engine is ready for use!")
    print("\n💡 Next steps:")
    print("   1. Start Neo4j database")
    print("   2. Test with real chunk indices")
    print("   3. Integrate with Pinecone vector search")
    
    return True

if __name__ == "__main__":
    main()
