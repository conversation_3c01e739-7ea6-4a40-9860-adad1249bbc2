# Enterprise KG Minimal - Environment Configuration Example
# Copy this file to .env and fill in your actual values

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_neo4j_password

# LLM Configuration (choose one or more)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Optional: Other LLM providers
# GEMINI_API_KEY=your_gemini_api_key_here
# OPENROUTER_API_KEY=your_openrouter_api_key_here
# REQUESTY_API_KEY=your_requesty_api_key_here
# REQUESTY_BASE_URL=https://router.requesty.ai/v1
