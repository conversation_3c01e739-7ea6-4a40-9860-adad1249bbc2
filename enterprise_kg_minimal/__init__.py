"""
Enterprise KG Minimal Package

A standalone package for enterprise knowledge graph processing that creates
chunk-based knowledge graphs from documents and provides hybrid search capabilities.

This package provides:
- Document chunking and processing
- Entity and relationship extraction per chunk
- Neo4j knowledge graph storage with chunk structure
- File → Chunks → Chunk Graphs architecture
- Hybrid search engine combining vector similarity with graph traversal

Usage:
    # Document processing
    from enterprise_kg_minimal import process_document

    result = process_document(
        file_id="doc_123",
        file_content="Your document content here...",
        neo4j_uri="bolt://localhost:7687",
        neo4j_user="neo4j",
        neo4j_password="password"
    )

    # Hybrid search (with chunk indices from vector search)
    from enterprise_kg_minimal import search_knowledge_graph

    search_result = search_knowledge_graph(
        chunk_indices=["chunk_1", "chunk_2"],  # From Pinecone/vector search
        query_text="Who manages the AI project?",
        strategy="hybrid"
    )
"""

from .core.document_processor import process_document

# Import search functionality
try:
    from .search.hybrid_search_engine import (
        HybridSearchEngine,
        create_hybrid_search_engine,
        search_with_chunk_indices
    )
    from .search.search_schemas import SearchStrategy

    # Convenience function for hybrid search
    def search_knowledge_graph(
        chunk_indices: list,
        query_text: str = None,
        neo4j_uri: str = "bolt://localhost:7687",
        neo4j_user: str = "neo4j",
        neo4j_password: str = "password",
        strategy: str = "hybrid",
        max_results: int = 50,
        expansion_depth: int = 2,
        entity_types: set = None,
        relationship_types: set = None
    ):
        """
        Search the knowledge graph using chunk indices from vector similarity search.

        This function is designed to work with chunk indices obtained from vector
        databases like Pinecone after similarity search, enriching them with graph context.

        Args:
            chunk_indices: List of chunk IDs from vector similarity search
            query_text: Optional query text for context
            neo4j_uri: Neo4j database URI
            neo4j_user: Neo4j username
            neo4j_password: Neo4j password
            strategy: Search strategy ("hybrid", "entity_centric", "relationship_centric", etc.)
            max_results: Maximum number of results to return
            expansion_depth: Graph traversal depth
            entity_types: Optional set of entity types to filter by
            relationship_types: Optional set of relationship types to filter by

        Returns:
            Dictionary with search results
        """
        try:
            # Convert string strategy to enum
            strategy_map = {
                "hybrid": SearchStrategy.HYBRID,
                "entity_centric": SearchStrategy.ENTITY_CENTRIC,
                "relationship_centric": SearchStrategy.RELATIONSHIP_CENTRIC,
                "chunk_expansion": SearchStrategy.CHUNK_EXPANSION,
                "hierarchical": SearchStrategy.HIERARCHICAL
            }

            search_strategy = strategy_map.get(strategy.lower(), SearchStrategy.HYBRID)

            # Execute search
            result = search_with_chunk_indices(
                chunk_indices=chunk_indices,
                query_text=query_text,
                neo4j_uri=neo4j_uri,
                neo4j_user=neo4j_user,
                neo4j_password=neo4j_password,
                strategy=search_strategy,
                max_results=max_results,
                expansion_depth=expansion_depth,
                entity_types=entity_types,
                relationship_types=relationship_types
            )

            # Convert to simple dictionary format
            return {
                "success": True,
                "total_results": result.total_results,
                "processing_time_ms": result.processing_time_ms,
                "strategy_used": result.strategy_used.value,
                "entities": [
                    {
                        "name": entity.name,
                        "type": entity.entity_type,
                        "relevance_score": entity.relevance_score,
                        "properties": entity.properties,
                        "chunk_sources": entity.chunk_sources
                    }
                    for entity in result.graph_context.entities
                ],
                "relationships": [
                    {
                        "source": rel.source_entity,
                        "target": rel.target_entity,
                        "type": rel.relationship_type,
                        "confidence_score": rel.confidence_score,
                        "relevance_score": rel.relevance_score,
                        "context": rel.context
                    }
                    for rel in result.graph_context.relationships
                ],
                "quality_scores": {
                    "coverage": result.coverage_score,
                    "coherence": result.coherence_score,
                    "relevance": result.relevance_score
                },
                "graph_statistics": {
                    "entity_types_found": list(result.graph_context.entity_types_found),
                    "relationship_types_found": list(result.graph_context.relationship_types_found),
                    "max_depth_reached": result.graph_context.max_depth_reached,
                    "source_chunks": result.graph_context.chunk_count
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "chunk_indices": chunk_indices
            }

    __all__ = [
        "process_document",
        "search_knowledge_graph",
        "HybridSearchEngine",
        "create_hybrid_search_engine",
        "search_with_chunk_indices"
    ]

except ImportError:
    # Search functionality not available (missing dependencies)
    __all__ = ["process_document"]

__version__ = "2.0.0"
