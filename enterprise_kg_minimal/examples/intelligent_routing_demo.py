"""
Intelligent Routing Demo for Enterprise KG Hybrid Search

This script demonstrates the new intelligent routing layer that determines
whether graph traversal is needed or if semantic search alone is sufficient.
"""

import logging
from typing import List, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import the search components
from enterprise_kg_minimal.search import (
    HybridSearchEngine,
    SearchRouter,
    QueryAnalyzer,
    VectorMetadataAnalyzer,
    RoutingDecision,
    QueryComplexity,
    SearchStrategy
)

def demo_intelligent_routing():
    """Demonstrate intelligent routing with various query types."""
    
    print("=" * 60)
    print("ENTERPRISE KG INTELLIGENT ROUTING DEMO")
    print("=" * 60)
    
    # Initialize components
    search_router = SearchRouter()
    query_analyzer = QueryAnalyzer()
    metadata_analyzer = VectorMetadataAnalyzer()
    
    # Test queries with different complexity levels
    test_queries = [
        {
            "query": "What is the company leave policy?",
            "description": "Simple factual query - should use semantic only",
            "chunk_indices": ["chunk_1", "chunk_2", "chunk_3"]
        },
        {
            "query": "Who reports to the CEO and what are their responsibilities?",
            "description": "Complex hierarchical query - should use full graph",
            "chunk_indices": ["chunk_4", "chunk_5", "chunk_6"]
        },
        {
            "query": "How are employees connected to their managers?",
            "description": "Relational query - should use graph traversal",
            "chunk_indices": ["chunk_7", "chunk_8"]
        },
        {
            "query": "Contact information for HR department",
            "description": "Simple contact query - should use semantic only",
            "chunk_indices": ["chunk_9", "chunk_10"]
        },
        {
            "query": "What is the relationship between IT department and security policies?",
            "description": "Moderate complexity - might use light graph",
            "chunk_indices": ["chunk_11", "chunk_12", "chunk_13"]
        }
    ]
    
    print("\n1. QUERY ANALYSIS DEMO")
    print("-" * 40)
    
    for i, test_case in enumerate(test_queries, 1):
        query_text = test_case["query"]
        description = test_case["description"]
        
        print(f"\nQuery {i}: {query_text}")
        print(f"Expected: {description}")
        
        # Analyze query
        analysis = query_analyzer.analyze_query(query_text)
        
        print(f"Complexity: {analysis.query_complexity.value}")
        print(f"Score: {analysis.complexity_score:.2f}")
        print(f"Recommendation: {analysis.recommended_decision.value}")
        print(f"Confidence: {analysis.confidence_score:.2f}")
        print(f"Reasoning: {analysis.reasoning}")
        
        if analysis.detected_entities:
            print(f"Entities: {', '.join(analysis.detected_entities)}")
        if analysis.detected_relationships:
            print(f"Relationships: {', '.join(analysis.detected_relationships)}")
    
    print("\n\n2. VECTOR METADATA ANALYSIS DEMO")
    print("-" * 40)
    
    # Demo with dummy metadata
    for i, test_case in enumerate(test_queries[:3], 1):
        query_text = test_case["query"]
        chunk_indices = test_case["chunk_indices"]
        
        print(f"\nQuery {i}: {query_text}")
        
        # Create dummy metadata
        dummy_metadata = metadata_analyzer.create_dummy_metadata(
            chunk_indices, query_text
        )
        
        # Analyze metadata sufficiency
        metadata_analysis = metadata_analyzer.analyze_metadata_sufficiency(
            query_text, dummy_metadata
        )
        
        print(f"Metadata sufficient: {metadata_analysis['is_sufficient']}")
        print(f"Sufficiency score: {metadata_analysis['sufficiency_score']:.2f}")
        print(f"Has direct answer: {metadata_analysis['has_direct_answer']}")
        print(f"Recommendation: {metadata_analysis['recommendation']}")
        print(f"Reasoning: {metadata_analysis['reasoning']}")
    
    print("\n\n3. INTELLIGENT ROUTING DEMO")
    print("-" * 40)
    
    for i, test_case in enumerate(test_queries, 1):
        query_text = test_case["query"]
        chunk_indices = test_case["chunk_indices"]
        description = test_case["description"]
        
        print(f"\nQuery {i}: {query_text}")
        print(f"Expected: {description}")
        
        # Create dummy metadata
        dummy_metadata = metadata_analyzer.create_dummy_metadata(
            chunk_indices, query_text
        )
        
        # Route the query
        routing_result = search_router.route_query(
            query_text=query_text,
            chunk_indices=chunk_indices,
            vector_metadata=dummy_metadata
        )
        
        print(f"Routing Decision: {routing_result.decision.value}")
        print(f"Confidence: {routing_result.confidence:.2f}")
        print(f"Skip Graph: {routing_result.should_skip_graph}")
        print(f"Suggested Strategy: {routing_result.suggested_strategy.value}")
        print(f"Max Depth: {routing_result.max_expansion_depth}")
        print(f"Estimated Time: {routing_result.estimated_time_ms:.1f}ms")
        print(f"Reasoning: {routing_result.reasoning}")
    
    print("\n\n4. MANUAL OVERRIDE DEMO")
    print("-" * 40)
    
    query_text = "What is the leave policy?"
    chunk_indices = ["chunk_1", "chunk_2"]
    
    print(f"Query: {query_text}")
    print("Testing manual overrides...")
    
    for override in [RoutingDecision.SEMANTIC_ONLY, RoutingDecision.LIGHT_GRAPH, RoutingDecision.FULL_GRAPH]:
        routing_result = search_router.route_query(
            query_text=query_text,
            chunk_indices=chunk_indices,
            override_decision=override
        )
        
        print(f"\nOverride: {override.value}")
        print(f"Decision: {routing_result.decision.value}")
        print(f"Strategy: {routing_result.suggested_strategy.value}")
        print(f"Max Depth: {routing_result.max_expansion_depth}")
    
    print("\n\n5. PERFORMANCE COMPARISON")
    print("-" * 40)
    
    print("Estimated performance for different routing decisions:")
    print("(These are simulated estimates)")
    
    decisions = [
        RoutingDecision.SEMANTIC_ONLY,
        RoutingDecision.LIGHT_GRAPH,
        RoutingDecision.FULL_GRAPH
    ]
    
    for decision in decisions:
        routing_result = search_router.route_query(
            query_text="Sample query",
            chunk_indices=["chunk_1", "chunk_2", "chunk_3"],
            override_decision=decision
        )
        
        print(f"\n{decision.value}:")
        print(f"  Estimated Time: {routing_result.estimated_time_ms:.1f}ms")
        print(f"  Estimated Cost: {routing_result.estimated_cost:.1f} units")
        print(f"  Graph Traversal: {'No' if routing_result.should_skip_graph else 'Yes'}")
    
    print("\n\n6. INTEGRATION WITH HYBRID SEARCH ENGINE")
    print("-" * 40)
    
    print("Note: To use with actual Neo4j database:")
    print("1. Ensure Neo4j is running")
    print("2. Create HybridSearchEngine with enable_intelligent_routing=True")
    print("3. Call search() with vector_metadata parameter")
    print("\nExample code:")
    print("""
from enterprise_kg_minimal.search import HybridSearchEngine, create_hybrid_search_engine

# Create engine with intelligent routing enabled (default)
search_engine = create_hybrid_search_engine(
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j", 
    neo4j_password="password",
    enable_intelligent_routing=True
)

# Search with automatic routing
result = search_engine.search(
    chunk_indices=["chunk_1", "chunk_2", "chunk_3"],
    query_text="What is the leave policy?",
    vector_metadata=dummy_metadata  # Optional: from Pinecone
)

# Check if graph traversal was skipped
if result.debug_info.get('graph_traversal_skipped'):
    print("Used semantic search only - faster execution!")
else:
    print("Used graph traversal - comprehensive results!")
""")
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETED")
    print("=" * 60)


if __name__ == "__main__":
    demo_intelligent_routing()
