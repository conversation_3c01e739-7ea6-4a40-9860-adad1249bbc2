"""
Example demonstrating the hybrid graph traversal optimization for reduced latency.

This example shows how to use the optimized hybrid retrieval approach that combines
vector similarity scores with graph connectivity to improve traversal performance.
"""

import logging
import time
from typing import List, Dict, Any

from ..storage.neo4j_client import Neo4jClient
from ..search.graph_rag import GraphRAG
from ..search.search_schemas import SearchQuery, SearchStrategy

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HybridOptimizationDemo:
    """
    Demonstrates the hybrid optimization techniques for graph traversal.
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """
        Initialize the demo with Neo4j client.
        
        Args:
            neo4j_client: Neo4j client for graph operations
        """
        self.neo4j_client = neo4j_client
        self.graph_rag = GraphRAG(neo4j_client)
    
    def compare_traversal_methods(
        self, 
        chunk_indices: List[str], 
        expansion_depth: int = 3
    ) -> Dict[str, Any]:
        """
        Compare traditional vs hybrid traversal methods.
        
        Args:
            chunk_indices: List of chunk IDs to start traversal from
            expansion_depth: Maximum depth for graph expansion
            
        Returns:
            Comparison results with timing and quality metrics
        """
        logger.info(f"Comparing traversal methods for {len(chunk_indices)} chunks, depth {expansion_depth}")
        
        results = {}
        
        # Test 1: Traditional BFS traversal
        traditional_query = SearchQuery(
            chunk_indices=chunk_indices,
            expansion_depth=expansion_depth,
            use_hybrid_traversal=False,  # Use traditional method
            max_results=100
        )
        
        start_time = time.time()
        traditional_context, traditional_metrics = self.graph_rag.extract_graph_context(
            chunk_indices, traditional_query
        )
        traditional_time = time.time() - start_time
        
        results['traditional'] = {
            'execution_time_ms': traditional_time * 1000,
            'entities_found': len(traditional_context.entities),
            'relationships_found': len(traditional_context.relationships),
            'expansion_iterations': traditional_metrics.expansion_iterations,
            'avg_entity_confidence': traditional_metrics.avg_entity_confidence,
            'max_depth_reached': traditional_context.max_depth_reached
        }
        
        # Test 2: Hybrid priority-based traversal
        hybrid_query = SearchQuery(
            chunk_indices=chunk_indices,
            expansion_depth=expansion_depth,
            use_hybrid_traversal=True,  # Use hybrid method
            max_results=100,
            # Optimized weights for performance
            connectivity_weight=0.3,
            cooccurrence_weight=0.2,
            confidence_weight=0.4,
            priority_inheritance_weight=0.1,
            priority_queue_limit=500  # Smaller limit for faster processing
        )
        
        start_time = time.time()
        hybrid_context, hybrid_metrics = self.graph_rag.extract_graph_context(
            chunk_indices, hybrid_query
        )
        hybrid_time = time.time() - start_time
        
        results['hybrid'] = {
            'execution_time_ms': hybrid_time * 1000,
            'entities_found': len(hybrid_context.entities),
            'relationships_found': len(hybrid_context.relationships),
            'expansion_iterations': hybrid_metrics.expansion_iterations,
            'avg_entity_confidence': hybrid_metrics.avg_entity_confidence,
            'max_depth_reached': hybrid_context.max_depth_reached
        }
        
        # Calculate performance improvements
        time_improvement = ((traditional_time - hybrid_time) / traditional_time) * 100
        quality_improvement = (
            (hybrid_metrics.avg_entity_confidence - traditional_metrics.avg_entity_confidence) 
            / traditional_metrics.avg_entity_confidence * 100
            if traditional_metrics.avg_entity_confidence > 0 else 0
        )
        
        results['comparison'] = {
            'time_improvement_percent': time_improvement,
            'quality_improvement_percent': quality_improvement,
            'hybrid_faster': hybrid_time < traditional_time,
            'hybrid_higher_quality': hybrid_metrics.avg_entity_confidence > traditional_metrics.avg_entity_confidence
        }
        
        logger.info(f"Performance comparison completed:")
        logger.info(f"  Traditional: {traditional_time*1000:.2f}ms, {len(traditional_context.entities)} entities")
        logger.info(f"  Hybrid: {hybrid_time*1000:.2f}ms, {len(hybrid_context.entities)} entities")
        logger.info(f"  Time improvement: {time_improvement:.1f}%")
        logger.info(f"  Quality improvement: {quality_improvement:.1f}%")
        
        return results
    
    def optimize_weights_for_domain(
        self, 
        chunk_indices: List[str], 
        test_configurations: List[Dict[str, float]]
    ) -> Dict[str, Any]:
        """
        Test different weight configurations to find optimal settings for your domain.
        
        Args:
            chunk_indices: Sample chunk indices for testing
            test_configurations: List of weight configurations to test
            
        Returns:
            Results for each configuration with performance metrics
        """
        logger.info(f"Testing {len(test_configurations)} weight configurations")
        
        results = []
        
        for i, config in enumerate(test_configurations):
            logger.info(f"Testing configuration {i+1}/{len(test_configurations)}: {config}")
            
            query = SearchQuery(
                chunk_indices=chunk_indices,
                expansion_depth=2,
                use_hybrid_traversal=True,
                max_results=50,
                **config  # Unpack the configuration
            )
            
            start_time = time.time()
            context, metrics = self.graph_rag.extract_graph_context(chunk_indices, query)
            execution_time = time.time() - start_time
            
            result = {
                'configuration': config,
                'execution_time_ms': execution_time * 1000,
                'entities_found': len(context.entities),
                'relationships_found': len(context.relationships),
                'avg_entity_confidence': metrics.avg_entity_confidence,
                'avg_relationship_confidence': metrics.avg_relationship_confidence,
                'expansion_iterations': metrics.expansion_iterations,
                'quality_score': (
                    metrics.avg_entity_confidence * 0.6 + 
                    metrics.avg_relationship_confidence * 0.4
                ),
                'efficiency_score': len(context.entities) / (execution_time * 1000)  # entities per ms
            }
            
            results.append(result)
        
        # Find best configuration
        best_quality = max(results, key=lambda x: x['quality_score'])
        best_speed = max(results, key=lambda x: x['efficiency_score'])
        
        return {
            'all_results': results,
            'best_quality_config': best_quality,
            'best_speed_config': best_speed,
            'recommendations': self._generate_recommendations(results)
        }
    
    def _generate_recommendations(self, results: List[Dict[str, Any]]) -> Dict[str, str]:
        """Generate recommendations based on test results."""
        
        # Analyze patterns in the results
        high_quality_results = [r for r in results if r['quality_score'] > 0.7]
        fast_results = [r for r in results if r['execution_time_ms'] < 100]
        
        recommendations = {}
        
        if high_quality_results:
            avg_connectivity_weight = sum(r['configuration']['connectivity_weight'] for r in high_quality_results) / len(high_quality_results)
            recommendations['high_quality'] = f"For high quality results, use connectivity_weight around {avg_connectivity_weight:.2f}"
        
        if fast_results:
            avg_queue_limit = sum(r['configuration'].get('priority_queue_limit', 1000) for r in fast_results) / len(fast_results)
            recommendations['high_speed'] = f"For fast execution, use priority_queue_limit around {avg_queue_limit:.0f}"
        
        recommendations['general'] = "Balance connectivity_weight (0.2-0.4) and confidence_weight (0.3-0.5) based on your use case"
        
        return recommendations


def run_optimization_demo():
    """
    Run the hybrid optimization demonstration.
    """
    # This would typically be called with your actual Neo4j client
    # neo4j_client = Neo4jClient(uri="bolt://localhost:7687", user="neo4j", password="password")
    # demo = HybridOptimizationDemo(neo4j_client)
    
    # Example chunk indices (replace with actual chunk IDs from your system)
    sample_chunk_indices = ["chunk_1", "chunk_2", "chunk_3", "chunk_4", "chunk_5"]
    
    # Test configurations for weight optimization
    test_configs = [
        {'connectivity_weight': 0.2, 'cooccurrence_weight': 0.3, 'confidence_weight': 0.4, 'priority_inheritance_weight': 0.1},
        {'connectivity_weight': 0.3, 'cooccurrence_weight': 0.2, 'confidence_weight': 0.4, 'priority_inheritance_weight': 0.1},
        {'connectivity_weight': 0.4, 'cooccurrence_weight': 0.2, 'confidence_weight': 0.3, 'priority_inheritance_weight': 0.1},
        {'connectivity_weight': 0.3, 'cooccurrence_weight': 0.1, 'confidence_weight': 0.5, 'priority_inheritance_weight': 0.1},
    ]
    
    print("Hybrid Graph Traversal Optimization Demo")
    print("=" * 50)
    print("This demo would compare traditional vs hybrid traversal methods")
    print("and help you find optimal weight configurations for your domain.")
    print("\nTo run this demo:")
    print("1. Initialize your Neo4j client")
    print("2. Create HybridOptimizationDemo instance")
    print("3. Call compare_traversal_methods() with your chunk indices")
    print("4. Call optimize_weights_for_domain() to tune parameters")


if __name__ == "__main__":
    run_optimization_demo()
