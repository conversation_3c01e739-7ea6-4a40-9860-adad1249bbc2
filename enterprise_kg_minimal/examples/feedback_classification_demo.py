#!/usr/bin/env python3
"""
Customer Feedback Classification Demo

This script demonstrates how to use the enterprise_kg_minimal system
for customer feedback classification and sentiment analysis.

Usage:
    python feedback_classification_demo.py
"""

import os
import sys
import json
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.sample_data.customer_feedback_samples import get_sample_feedback_data, generate_feedback_file_content
from enterprise_kg_minimal.utils.feedback_analyzer import analyze_feedback_batch, generate_feedback_report

def demo_single_feedback_processing():
    """Demonstrate processing a single customer feedback."""
    print("🔍 Demo: Processing Single Customer Feedback")
    print("=" * 50)
    
    # Get a sample feedback
    sample_feedback = get_sample_feedback_data()[0]  # First positive feedback
    
    print(f"📝 Processing feedback from: {sample_feedback['customer_name']}")
    print(f"📦 Product/Service: {sample_feedback['product_service']}")
    print(f"😊 Expected Sentiment: {sample_feedback['expected_sentiment']}")
    print()
    
    # Generate file content
    file_content = generate_feedback_file_content(sample_feedback)
    file_id = f"demo_{sample_feedback['feedback_id']}"
    
    print("📄 Feedback Content:")
    print("-" * 30)
    print(file_content[:300] + "..." if len(file_content) > 300 else file_content)
    print("-" * 30)
    print()
    
    try:
        # Process the feedback
        print("⚙️ Processing with enterprise_kg_minimal...")
        result = process_document(
            file_id=file_id,
            file_content=file_content,
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="password",
            llm_provider="openai",
            llm_model="gpt-4o",
            chunking_strategy="hybrid",
            chunk_size=800,
            chunk_overlap=100
        )
        
        print("✅ Processing completed!")
        print(f"📊 Results:")
        print(f"   - Success: {result.get('success', False)}")
        print(f"   - Chunks created: {result.get('chunks_created', 0)}")
        print(f"   - Entities extracted: {result.get('total_entities', 0)}")
        print(f"   - Relationships extracted: {result.get('total_relationships', 0)}")
        
        if result.get('chunk_details'):
            print(f"\n📋 Chunk Details:")
            for i, chunk in enumerate(result['chunk_details']):
                print(f"   Chunk {i+1}:")
                print(f"     - Entities: {chunk.get('entities_extracted', 0)}")
                print(f"     - Relationships: {chunk.get('relationships_extracted', 0)}")
                print(f"     - Success: {chunk.get('graph_stored', False)}")
        
        return result
        
    except Exception as e:
        print(f"❌ Error processing feedback: {str(e)}")
        return None

def demo_batch_feedback_analysis():
    """Demonstrate batch processing and analysis of multiple feedback samples."""
    print("\n🔄 Demo: Batch Feedback Analysis")
    print("=" * 50)
    
    # Get sample feedback data
    sample_feedbacks = get_sample_feedback_data()[:5]  # Process first 5 samples
    
    print(f"📊 Processing {len(sample_feedbacks)} feedback samples...")
    
    results = []
    
    for i, feedback in enumerate(sample_feedbacks):
        print(f"   Processing {i+1}/{len(sample_feedbacks)}: {feedback['feedback_id']}")
        
        try:
            file_content = generate_feedback_file_content(feedback)
            file_id = f"batch_demo_{feedback['feedback_id']}"
            
            result = process_document(
                file_id=file_id,
                file_content=file_content,
                neo4j_uri="bolt://localhost:7687",
                neo4j_user="neo4j",
                neo4j_password="password",
                llm_provider="openai",
                llm_model="gpt-4o",
                chunking_strategy="hybrid",
                chunk_size=800,
                chunk_overlap=100
            )
            
            # Add original feedback data for analysis
            result['original_feedback'] = feedback
            results.append(result)
            
        except Exception as e:
            print(f"     ❌ Error: {str(e)}")
            continue
    
    print(f"\n✅ Batch processing completed!")
    print(f"📈 Successfully processed: {len(results)} out of {len(sample_feedbacks)} samples")
    
    # Analyze results
    if results:
        print("\n📊 Generating analysis report...")
        analysis = analyze_feedback_batch(results)
        report = generate_feedback_report(analysis)
        
        # Save report
        report_filename = f"demo_feedback_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w') as f:
            f.write(report)
        
        print(f"📄 Analysis report saved to: {report_filename}")
        
        # Display summary
        print(f"\n📋 Analysis Summary:")
        summary = analysis['summary']
        print(f"   - Average sentiment score: {summary['key_metrics']['average_sentiment']:.2f}")
        print(f"   - Negative feedback ratio: {summary['key_metrics']['negative_feedback_ratio']:.1%}")
        print(f"   - Total complaints identified: {summary['key_metrics']['total_complaints']}")
        
        # Show recommendations
        recommendations = analysis.get('recommendations', [])
        if recommendations:
            print(f"\n💡 Top Recommendations:")
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"   {i}. {rec['title']} (Priority: {rec['priority']})")
    
    return results

def demo_sentiment_classification():
    """Demonstrate sentiment classification capabilities."""
    print("\n😊 Demo: Sentiment Classification")
    print("=" * 50)
    
    # Test different sentiment types
    test_cases = [
        {
            "name": "Positive Feedback",
            "content": "I absolutely love this product! The customer service is outstanding and the features work perfectly. Highly recommended!",
            "expected": "positive"
        },
        {
            "name": "Negative Feedback", 
            "content": "This software is terrible. It crashes constantly and customer support is unresponsive. Very disappointed with this purchase.",
            "expected": "negative"
        },
        {
            "name": "Mixed Feedback",
            "content": "The product has some good features but also several issues. Customer support is helpful but the interface needs improvement.",
            "expected": "mixed"
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 Test Case {i}: {test_case['name']}")
        print(f"Expected Sentiment: {test_case['expected']}")
        print(f"Content: {test_case['content'][:100]}...")
        
        # Create a simple feedback document
        feedback_doc = f"""
Customer Feedback Report

Feedback ID: TEST_{i:03d}
Customer: Test Customer {i}
Product/Service: Demo Product
Date: {datetime.now().strftime('%Y-%m-%d')}

Feedback Content:
{test_case['content']}
"""
        
        try:
            result = process_document(
                file_id=f"sentiment_test_{i}",
                file_content=feedback_doc,
                neo4j_uri="bolt://localhost:7687",
                neo4j_user="neo4j",
                neo4j_password="password",
                llm_provider="openai",
                llm_model="gpt-4o"
            )
            
            print(f"✅ Processed successfully")
            print(f"   - Entities: {result.get('total_entities', 0)}")
            print(f"   - Relationships: {result.get('total_relationships', 0)}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")

def demo_neo4j_queries():
    """Demonstrate useful Neo4j queries for feedback analysis."""
    print("\n🔍 Demo: Useful Neo4j Queries for Feedback Analysis")
    print("=" * 50)
    
    queries = [
        {
            "name": "Find all customer feedback",
            "query": """
            MATCH (f:File)-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
            WHERE f.id STARTS WITH 'feedback_' OR f.id STARTS WITH 'demo_'
            RETURN f.id as feedback_file, e.name as entity, e.entity_type as type
            LIMIT 20
            """
        },
        {
            "name": "Find sentiment-related entities",
            "query": """
            MATCH (e:Entity)
            WHERE e.entity_type IN ['Sentiment', 'Feedback', 'Complaint', 'Compliment']
            RETURN e.name, e.entity_type, e.description
            LIMIT 10
            """
        },
        {
            "name": "Find customer-product relationships",
            "query": """
            MATCH (customer:Entity)-[r]->(product:Entity)
            WHERE customer.entity_type = 'Customer' 
            AND product.entity_type IN ['Product', 'Service']
            RETURN customer.name, type(r), product.name
            LIMIT 10
            """
        },
        {
            "name": "Find feedback relationships",
            "query": """
            MATCH (e1:Entity)-[r]->(e2:Entity)
            WHERE type(r) IN ['provides_feedback', 'expresses_sentiment', 'rates', 'complains_about', 'praises']
            RETURN e1.name, type(r), e2.name
            LIMIT 15
            """
        }
    ]
    
    print("📋 Useful queries for analyzing feedback data in Neo4j:")
    print()
    
    for i, query_info in enumerate(queries, 1):
        print(f"{i}. {query_info['name']}:")
        print("```cypher")
        print(query_info['query'].strip())
        print("```")
        print()

def main():
    """Main demo function."""
    print("🎯 Customer Feedback Classification Demo")
    print("🏢 Enterprise KG Minimal System")
    print("=" * 60)
    
    print("\nThis demo showcases the customer feedback classification capabilities")
    print("of the enterprise_kg_minimal system, including:")
    print("• Sentiment analysis and classification")
    print("• Entity extraction from feedback text")
    print("• Relationship mapping between customers, products, and feedback")
    print("• Batch processing and analysis")
    print("• Actionable insights generation")
    
    try:
        # Run demos
        demo_single_feedback_processing()
        demo_batch_feedback_analysis()
        demo_sentiment_classification()
        demo_neo4j_queries()
        
        print("\n🎉 Demo completed successfully!")
        print("\n📚 Next Steps:")
        print("1. Run the full test suite: python test_feedback_classification.py")
        print("2. Explore the Neo4j database with the provided queries")
        print("3. Customize entity types and relationships for your use case")
        print("4. Integrate with your existing feedback collection systems")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
