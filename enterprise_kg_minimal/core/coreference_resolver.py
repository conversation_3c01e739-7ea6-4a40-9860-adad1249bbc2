"""
Coreference Resolution Module for Enterprise KG

This module provides coreference resolution capabilities to identify and link
entities that refer to the same real-world object across different mentions
in documents and chunks.
"""

import logging
import re
from typing import Dict, List, Set, Tuple, Optional, Any
from collections import defaultdict
from difflib import SequenceMatcher

logger = logging.getLogger(__name__)


class CoreferenceResolver:
    """
    Resolves coreferences between entity mentions to create unified entity representations.
    
    Handles:
    - Name variations (<PERSON>, <PERSON>, Mr. <PERSON>)
    - Pronoun resolution (he, she, it, they)
    - Title variations (<PERSON><PERSON>, <PERSON>)
    - Abbreviations and acronyms (IBM, International Business Machines)
    - Organizational variations (Apple Inc., Apple, Apple Computer)
    """
    
    def __init__(self):
        """Initialize the coreference resolver."""
        self.entity_clusters = defaultdict(set)  # canonical_name -> {all_variations}
        self.mention_to_canonical = {}  # mention -> canonical_name
        self.entity_contexts = defaultdict(list)  # canonical_name -> [context_info]
        
        # Common pronouns and their likely entity types
        self.pronouns = {
            'he': ['Person', 'Employee', 'Manager', 'Executive'],
            'she': ['Person', 'Employee', 'Manager', 'Executive'],
            'him': ['Person', 'Employee', 'Manager', 'Executive'],
            'her': ['Person', 'Employee', 'Manager', 'Executive'],
            'it': ['System', 'Application', 'Platform', 'Tool', 'Company', 'Project'],
            'they': ['Team', 'Department', 'Company', 'Group'],
            'them': ['Team', 'Department', 'Company', 'Group'],
            'this': ['System', 'Project', 'Initiative', 'Document'],
            'that': ['System', 'Project', 'Initiative', 'Document']
        }
        
        # Common titles and prefixes
        self.titles = {
            'mr', 'mrs', 'ms', 'miss', 'dr', 'prof', 'professor', 'ceo', 'cto', 'cfo',
            'president', 'director', 'manager', 'lead', 'senior', 'junior', 'sr', 'jr'
        }
        
        # Company suffixes
        self.company_suffixes = {
            'inc', 'corp', 'corporation', 'ltd', 'limited', 'llc', 'co', 'company',
            'enterprises', 'systems', 'solutions', 'technologies', 'tech'
        }
    
    def resolve_entities(self, entities_by_chunk: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """
        Resolve coreferences across all entities from multiple chunks.
        
        Args:
            entities_by_chunk: Dictionary mapping chunk_id to list of entity dictionaries
            
        Returns:
            Dictionary with resolved entities and mapping information
        """
        logger.info("Starting coreference resolution across chunks")
        
        # Step 1: Collect all entities and their contexts
        all_entities = []
        for chunk_id, entities in entities_by_chunk.items():
            for entity in entities:
                entity['chunk_id'] = chunk_id
                all_entities.append(entity)
        
        logger.info(f"Processing {len(all_entities)} entities across {len(entities_by_chunk)} chunks")
        
        # Step 2: Group entities by type for more accurate matching
        entities_by_type = defaultdict(list)
        for entity in all_entities:
            entity_type = entity.get('entity_type', 'Entity')
            entities_by_type[entity_type].append(entity)
        
        # Step 3: Resolve coreferences within each entity type
        resolution_results = {}
        for entity_type, entities in entities_by_type.items():
            logger.info(f"Resolving {len(entities)} entities of type '{entity_type}'")
            type_results = self._resolve_entities_by_type(entities, entity_type)
            resolution_results[entity_type] = type_results
        
        # Step 4: Create final mapping and statistics
        final_mapping = {}
        total_clusters = 0
        total_resolved = 0
        
        for entity_type, type_results in resolution_results.items():
            final_mapping.update(type_results['mention_to_canonical'])
            total_clusters += len(type_results['clusters'])
            total_resolved += type_results['resolved_count']
        
        logger.info(f"Coreference resolution completed: {total_resolved} entities resolved into {total_clusters} clusters")
        
        return {
            'mention_to_canonical': final_mapping,
            'clusters_by_type': {et: tr['clusters'] for et, tr in resolution_results.items()},
            'statistics': {
                'total_entities': len(all_entities),
                'total_clusters': total_clusters,
                'total_resolved': total_resolved,
                'resolution_rate': total_resolved / len(all_entities) if all_entities else 0
            }
        }
    
    def _resolve_entities_by_type(self, entities: List[Dict[str, Any]], entity_type: str) -> Dict[str, Any]:
        """Resolve coreferences for entities of a specific type."""
        clusters = []
        mention_to_canonical = {}
        
        # Sort entities by name length (longer names first for better canonical selection)
        entities.sort(key=lambda x: len(x.get('name', '')), reverse=True)
        
        for entity in entities:
            name = entity.get('name', '').strip()
            if not name:
                continue
                
            # Find if this entity belongs to an existing cluster
            matched_cluster = None
            for cluster in clusters:
                if self._should_merge_with_cluster(name, cluster, entity_type):
                    matched_cluster = cluster
                    break
            
            if matched_cluster:
                # Add to existing cluster
                matched_cluster['mentions'].add(name)
                matched_cluster['entities'].append(entity)
                mention_to_canonical[name] = matched_cluster['canonical']
            else:
                # Create new cluster
                canonical_name = self._get_canonical_name(name, entity_type)
                new_cluster = {
                    'canonical': canonical_name,
                    'mentions': {name},
                    'entities': [entity],
                    'entity_type': entity_type
                }
                clusters.append(new_cluster)
                mention_to_canonical[name] = canonical_name
        
        return {
            'clusters': clusters,
            'mention_to_canonical': mention_to_canonical,
            'resolved_count': sum(len(cluster['mentions']) for cluster in clusters)
        }
    
    def _should_merge_with_cluster(self, name: str, cluster: Dict[str, Any], entity_type: str) -> bool:
        """Determine if a name should be merged with an existing cluster."""
        canonical = cluster['canonical']
        existing_mentions = cluster['mentions']
        
        # Check for exact match
        if name in existing_mentions:
            return True
        
        # Check for fuzzy name matching
        for mention in existing_mentions:
            if self._are_names_coreferent(name, mention, entity_type):
                return True
        
        # Check against canonical name
        if self._are_names_coreferent(name, canonical, entity_type):
            return True
        
        return False
    
    def _are_names_coreferent(self, name1: str, name2: str, entity_type: str) -> bool:
        """Determine if two names refer to the same entity."""
        name1_clean = self._normalize_name(name1)
        name2_clean = self._normalize_name(name2)
        
        # Exact match after normalization
        if name1_clean == name2_clean:
            return True
        
        # Handle pronouns
        if name1.lower() in self.pronouns or name2.lower() in self.pronouns:
            return self._check_pronoun_coreference(name1, name2, entity_type)
        
        # Handle person names
        if entity_type in ['Person', 'Employee', 'Manager', 'Executive']:
            return self._check_person_name_coreference(name1_clean, name2_clean)
        
        # Handle organization names
        if entity_type in ['Company', 'Organization', 'Department', 'Team']:
            return self._check_organization_coreference(name1_clean, name2_clean)
        
        # Handle system/technology names
        if entity_type in ['System', 'Application', 'Platform', 'Tool']:
            return self._check_system_coreference(name1_clean, name2_clean)
        
        # General fuzzy matching for other types
        return self._fuzzy_match(name1_clean, name2_clean, threshold=0.85)
    
    def _normalize_name(self, name: str) -> str:
        """Normalize a name for comparison."""
        # Convert to lowercase and remove extra whitespace
        normalized = re.sub(r'\s+', ' ', name.lower().strip())
        
        # Remove common punctuation
        normalized = re.sub(r'[.,;:!?()"]', '', normalized)
        
        return normalized
    
    def _check_pronoun_coreference(self, name1: str, name2: str, entity_type: str) -> bool:
        """Check if a pronoun can refer to an entity type."""
        pronoun = name1.lower() if name1.lower() in self.pronouns else name2.lower()
        if pronoun in self.pronouns:
            return entity_type in self.pronouns[pronoun]
        return False
    
    def _check_person_name_coreference(self, name1: str, name2: str) -> bool:
        """Check if two person names are coreferent."""
        # Remove titles
        name1_no_title = self._remove_titles(name1)
        name2_no_title = self._remove_titles(name2)
        
        # Split into parts
        parts1 = name1_no_title.split()
        parts2 = name2_no_title.split()
        
        if not parts1 or not parts2:
            return False
        
        # Check if one is a subset of the other (e.g., "John" vs "John Smith")
        if set(parts1).issubset(set(parts2)) or set(parts2).issubset(set(parts1)):
            return True
        
        # Check for first name + last name matching
        if len(parts1) >= 2 and len(parts2) >= 2:
            # Compare first and last names
            if parts1[0] == parts2[0] and parts1[-1] == parts2[-1]:
                return True
        
        # Check for initials (e.g., "J. Smith" vs "John Smith")
        if self._check_initials_match(parts1, parts2):
            return True
        
        return False
    
    def _check_organization_coreference(self, name1: str, name2: str) -> bool:
        """Check if two organization names are coreferent."""
        # Remove common suffixes
        name1_clean = self._remove_company_suffixes(name1)
        name2_clean = self._remove_company_suffixes(name2)
        
        # Check if one is contained in the other
        if name1_clean in name2_clean or name2_clean in name1_clean:
            return True
        
        # Check for acronym matching (e.g., "IBM" vs "International Business Machines")
        if self._check_acronym_match(name1_clean, name2_clean):
            return True
        
        # Fuzzy matching for organizations
        return self._fuzzy_match(name1_clean, name2_clean, threshold=0.8)
    
    def _check_system_coreference(self, name1: str, name2: str) -> bool:
        """Check if two system names are coreferent."""
        # Check if one is contained in the other
        if name1 in name2 or name2 in name1:
            return True
        
        # Check for version differences (e.g., "CRM System v2" vs "CRM System")
        name1_no_version = re.sub(r'\s*v?\d+(\.\d+)*\s*', ' ', name1).strip()
        name2_no_version = re.sub(r'\s*v?\d+(\.\d+)*\s*', ' ', name2).strip()
        
        if name1_no_version == name2_no_version:
            return True
        
        return self._fuzzy_match(name1, name2, threshold=0.85)
    
    def _remove_titles(self, name: str) -> str:
        """Remove titles from a person's name."""
        words = name.split()
        filtered_words = []
        
        for word in words:
            word_clean = word.rstrip('.')
            if word_clean.lower() not in self.titles:
                filtered_words.append(word)
        
        return ' '.join(filtered_words)
    
    def _remove_company_suffixes(self, name: str) -> str:
        """Remove common company suffixes."""
        words = name.split()
        while words and words[-1].lower() in self.company_suffixes:
            words.pop()
        return ' '.join(words)
    
    def _check_initials_match(self, parts1: List[str], parts2: List[str]) -> bool:
        """Check if names match with initials."""
        # Simple initial matching logic
        for i, (p1, p2) in enumerate(zip(parts1, parts2)):
            if len(p1) == 1 and p1.lower() == p2[0].lower():
                continue
            elif len(p2) == 1 and p2.lower() == p1[0].lower():
                continue
            elif p1.lower() != p2.lower():
                return False
        return True
    
    def _check_acronym_match(self, name1: str, name2: str) -> bool:
        """Check if one name is an acronym of the other."""
        # Simple acronym checking
        if len(name1) <= 5 and len(name2) > 10:
            acronym = ''.join(word[0] for word in name2.split() if word)
            return name1.lower() == acronym.lower()
        elif len(name2) <= 5 and len(name1) > 10:
            acronym = ''.join(word[0] for word in name1.split() if word)
            return name2.lower() == acronym.lower()
        return False
    
    def _fuzzy_match(self, name1: str, name2: str, threshold: float = 0.8) -> bool:
        """Check if two names are similar enough to be considered the same."""
        similarity = SequenceMatcher(None, name1, name2).ratio()
        return similarity >= threshold
    
    def _get_canonical_name(self, name: str, entity_type: str) -> str:
        """Get the canonical form of a name."""
        # For now, use the original name as canonical
        # In a more sophisticated system, this could:
        # - Choose the most complete form
        # - Apply standardization rules
        # - Use external knowledge bases
        return name.strip()
    
    def get_resolution_statistics(self) -> Dict[str, Any]:
        """Get statistics about the coreference resolution."""
        total_mentions = len(self.mention_to_canonical)
        unique_entities = len(set(self.mention_to_canonical.values()))
        
        return {
            'total_mentions': total_mentions,
            'unique_entities': unique_entities,
            'resolution_rate': (total_mentions - unique_entities) / total_mentions if total_mentions > 0 else 0,
            'clusters': len(self.entity_clusters)
        }
