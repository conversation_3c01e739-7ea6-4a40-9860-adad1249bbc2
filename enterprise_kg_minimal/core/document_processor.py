"""
Main Document Processor for Enterprise KG Minimal

This module provides the main importable function that processes documents
and creates chunk-based knowledge graphs.
"""

import logging
from typing import List, Dict, Any, Optional

from ..llm.client import LLMClient
from ..storage.neo4j_client import Neo4jClient, Neo4jConnection
from ..constants.schemas import EntityRelationship
from .prompt_generator import create_full_prompt_generator
from .chunking_engine import ChunkingEngine, ChunkingStrategy
from .graph_builder import GraphBuilder, DocumentGraphResult

logger = logging.getLogger(__name__)


def process_document(
    file_id: str,
    file_content: str,
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j", 
    neo4j_password: str = "password",
    neo4j_database: Optional[str] = None,
    llm_provider: str = "openai",
    llm_model: str = "gpt-4o",
    llm_api_key: Optional[str] = None,
    chunking_strategy: str = "hybrid",
    chunk_size: int = 1000,
    chunk_overlap: int = 200
) -> Dict[str, Any]:
    """
    Process a document and create a chunk-based knowledge graph.
    
    This function:
    1. Chunks the document content
    2. Extracts entities and relationships from each chunk
    3. Creates a graph structure: File → Chunks → Chunk Graphs
    4. Stores everything in Neo4j
    
    Args:
        file_id: Unique identifier for the file (will be used as node ID)
        file_content: Content of the document to process
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        neo4j_database: Neo4j database name (optional)
        llm_provider: LLM provider (openai, anthropic, etc.)
        llm_model: LLM model name
        llm_api_key: LLM API key (optional, will use environment variable)
        chunking_strategy: Chunking strategy (fixed_size, sentence_based, paragraph_based, semantic_based, hybrid)
        chunk_size: Target chunk size in characters
        chunk_overlap: Overlap between chunks in characters
        
    Returns:
        Dictionary containing processing results and graph structure information
    """
    logger.info(f"Processing document {file_id} with {len(file_content)} characters")
    
    try:
        # Step 1: Initialize clients
        neo4j_conn = Neo4jConnection(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            database=neo4j_database
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        llm_client = LLMClient(
            provider=llm_provider,
            model=llm_model,
            api_key=llm_api_key
        )
        
        # Step 2: Initialize processing components
        chunking_engine = ChunkingEngine(
            strategy=ChunkingStrategy(chunking_strategy),
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        graph_builder = GraphBuilder(neo4j_client)
        prompt_generator = create_full_prompt_generator()
        
        # Step 3: Chunk the document
        logger.info(f"Chunking document using {chunking_strategy} strategy")
        chunks = chunking_engine.chunk_document(file_content, file_id)
        
        if not chunks:
            return {
                "success": False,
                "error": "No chunks were created from the document",
                "file_id": file_id,
                "chunks_created": 0
            }
        
        logger.info(f"Created {len(chunks)} chunks")
        
        # Step 4: Extract relationships from each chunk
        logger.info("Extracting entities and relationships from chunks")
        chunk_relationships = []
        
        for i, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)}: {chunk.metadata.chunk_id}")
            
            try:
                # Generate extraction prompt for this chunk
                prompt = prompt_generator.generate_relationship_extraction_prompt(chunk.text)
                schema_description = prompt_generator.get_schema_description()
                
                # Extract relationships using LLM
                response = llm_client.generate_structured_response(prompt, schema_description)
                
                # Parse relationships
                relationships = []
                if isinstance(response, list):
                    for rel_data in response:
                        try:
                            rel = EntityRelationship(**rel_data)
                            relationships.append(rel)
                        except Exception as e:
                            logger.warning(f"Failed to parse relationship in chunk {chunk.metadata.chunk_id}: {e}")
                
                chunk_relationships.append(relationships)
                logger.info(f"Extracted {len(relationships)} relationships from chunk {chunk.metadata.chunk_id}")
                
            except Exception as e:
                logger.error(f"Failed to extract relationships from chunk {chunk.metadata.chunk_id}: {e}")
                chunk_relationships.append([])  # Empty list for failed chunk
        
        # Step 5: Build the graph structure
        logger.info("Building graph structure in Neo4j")
        graph_result = graph_builder.build_document_graph(file_id, chunks, chunk_relationships)
        
        # Step 6: Prepare result
        result = {
            "success": True,
            "file_id": file_id,
            "chunks_created": len(chunks),
            "chunks_processed": graph_result.successful_chunks,
            "chunks_failed": graph_result.failed_chunks,
            "total_entities": graph_result.total_entities,
            "total_relationships": graph_result.total_relationships,
            "file_node_created": graph_result.file_node_created,
            "contains_relationships_created": graph_result.contains_relationships_created,
            "chunk_details": [
                {
                    "chunk_id": result.chunk_id,
                    "chunk_index": result.chunk_index,
                    "entities_extracted": result.entities_extracted,
                    "relationships_extracted": result.relationships_extracted,
                    "graph_stored": result.graph_stored,
                    "error": result.error
                }
                for result in graph_result.chunk_results
            ]
        }
        
        logger.info(f"Document processing completed: {graph_result.successful_chunks}/{len(chunks)} chunks processed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Failed to process document {file_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_id": file_id,
            "chunks_created": 0
        }
    
    finally:
        # Clean up connections
        try:
            neo4j_client.close()
        except:
            pass
