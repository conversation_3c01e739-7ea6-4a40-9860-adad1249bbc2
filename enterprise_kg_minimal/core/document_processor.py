"""
Main Document Processor for Enterprise KG Minimal

This module provides the main importable function that processes documents
and creates chunk-based knowledge graphs.
"""

import logging
from typing import List, Dict, Any, Optional

from ..llm.client import LLMClient
from ..storage.neo4j_client import Neo4jClient, Neo4jConnection
from ..constants.schemas import EntityRelationship
from .prompt_generator import create_full_prompt_generator, create_feedback_focused_generator
from .chunking_engine import ChunkingEngine, ChunkingStrategy
from .graph_builder import GraphBuilder
from .coreference_resolver import CoreferenceResolver

logger = logging.getLogger(__name__)


def detect_content_type(file_content: str, file_id: str) -> str:
    """
    Detect the type of content to determine appropriate processing strategy.

    Args:
        file_content: The document content to analyze
        file_id: The file identifier (may contain hints)

    Returns:
        Content type: 'feedback', 'project', 'general'
    """
    content_lower = file_content.lower()
    file_id_lower = file_id.lower()

    # Check for feedback-related keywords in file_id
    feedback_id_keywords = ['feedback', 'review', 'complaint', 'survey', 'rating', 'customer', 'satisfaction']
    if any(keyword in file_id_lower for keyword in feedback_id_keywords):
        return 'feedback'

    # Check for feedback-related keywords in content
    feedback_content_keywords = [
        'feedback', 'review', 'complaint', 'satisfied', 'disappointed', 'recommend',
        'customer service', 'support', 'rating', 'stars', 'experience', 'opinion',
        'terrible', 'excellent', 'love', 'hate', 'frustrated', 'happy',
        'issue', 'problem', 'bug', 'feature request', 'improvement'
    ]

    feedback_score = sum(1 for keyword in feedback_content_keywords if keyword in content_lower)

    # Check for project-related keywords
    project_keywords = [
        'project', 'initiative', 'milestone', 'deadline', 'deliverable',
        'stakeholder', 'requirements', 'scope', 'timeline', 'budget'
    ]

    project_score = sum(1 for keyword in project_keywords if keyword in content_lower)

    # Determine content type based on keyword density
    if feedback_score >= 3:  # At least 3 feedback-related keywords
        return 'feedback'
    elif project_score >= 3:  # At least 3 project-related keywords
        return 'project'
    else:
        return 'general'


def process_document(
    file_id: str,
    file_content: str,
    neo4j_uri: str = "bolt://localhost:7687",
    neo4j_user: str = "neo4j",
    neo4j_password: str = "password",
    neo4j_database: Optional[str] = None,
    llm_provider: str = "openai",
    llm_model: str = "gpt-4o",
    llm_api_key: Optional[str] = None,
    chunking_strategy: str = "hybrid",
    chunk_size: int = 1000,
    chunk_overlap: int = 200,
    content_type: Optional[str] = None,
    enable_coreference_resolution: bool = True
) -> Dict[str, Any]:
    """
    Process a document and create a chunk-based knowledge graph.
    
    This function:
    1. Chunks the document content
    2. Extracts entities and relationships from each chunk
    3. Creates a graph structure: File → Chunks → Chunk Graphs
    4. Stores everything in Neo4j
    
    Args:
        file_id: Unique identifier for the file (will be used as node ID)
        file_content: Content of the document to process
        neo4j_uri: Neo4j connection URI
        neo4j_user: Neo4j username
        neo4j_password: Neo4j password
        neo4j_database: Neo4j database name (optional)
        llm_provider: LLM provider (openai, anthropic, etc.)
        llm_model: LLM model name
        llm_api_key: LLM API key (optional, will use environment variable)
        chunking_strategy: Chunking strategy (fixed_size, sentence_based, paragraph_based, semantic_based, hybrid)
        chunk_size: Target chunk size in characters
        chunk_overlap: Overlap between chunks in characters
        content_type: Manual override for content type ("feedback", "project", "general")
                     If None, will auto-detect based on content and file_id
        enable_coreference_resolution: Whether to enable coreference resolution across chunks

    Returns:
        Dictionary containing processing results, graph structure information,
        detected/used content type, and coreference resolution statistics
    """
    logger.info(f"Processing document {file_id} with {len(file_content)} characters")
    
    try:
        # Step 1: Initialize clients
        neo4j_conn = Neo4jConnection(
            uri=neo4j_uri,
            user=neo4j_user,
            password=neo4j_password,
            database=neo4j_database
        )
        neo4j_client = Neo4jClient(neo4j_conn)
        
        llm_client = LLMClient(
            provider=llm_provider,
            model=llm_model,
            api_key=llm_api_key
        )
        
        # Step 2: Initialize processing components
        chunking_engine = ChunkingEngine(
            strategy=ChunkingStrategy(chunking_strategy),
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap
        )
        
        graph_builder = GraphBuilder(neo4j_client)

        # Step 2.5: Detect content type and choose appropriate prompt generator
        if content_type is None:
            content_type = detect_content_type(file_content, file_id)
            logger.info(f"Auto-detected content type: {content_type}")
        else:
            logger.info(f"Using manual content type override: {content_type}")

        if content_type == 'feedback':
            prompt_generator = create_feedback_focused_generator()
            logger.info("Using feedback-focused prompt generator with sentiment analysis")
        else:
            prompt_generator = create_full_prompt_generator()
            logger.info("Using general prompt generator")
        
        # Step 3: Chunk the document
        logger.info(f"Chunking document using {chunking_strategy} strategy")
        chunks = chunking_engine.chunk_document(file_content, file_id)
        
        if not chunks:
            return {
                "success": False,
                "error": "No chunks were created from the document",
                "file_id": file_id,
                "chunks_created": 0
            }
        
        logger.info(f"Created {len(chunks)} chunks")
        
        # Step 4: Extract relationships from each chunk
        logger.info("Extracting entities and relationships from chunks")
        chunk_relationships = []

        # Initialize coreference resolver if enabled
        coreference_resolver = CoreferenceResolver() if enable_coreference_resolution else None
        previous_entities = []  # Track entities from previous chunks for coreference

        for i, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)}: {chunk.metadata.chunk_id}")

            try:
                # Generate extraction prompt for this chunk based on content type
                if content_type == 'feedback':
                    prompt = prompt_generator.generate_feedback_sentiment_prompt(chunk.text)
                    logger.debug(f"Using feedback sentiment prompt for chunk {chunk.metadata.chunk_id}")
                elif enable_coreference_resolution and previous_entities:
                    # Use coreference-aware prompt with context from previous chunks
                    prompt = prompt_generator.generate_coreference_aware_prompt(chunk.text, previous_entities)
                    logger.debug(f"Using coreference-aware prompt for chunk {chunk.metadata.chunk_id}")
                else:
                    prompt = prompt_generator.generate_relationship_extraction_prompt(chunk.text)
                    logger.debug(f"Using general relationship extraction prompt for chunk {chunk.metadata.chunk_id}")

                schema_description = prompt_generator.get_schema_description()
                
                # Extract relationships using LLM
                response = llm_client.generate_structured_response(prompt, schema_description)
                
                # Parse relationships
                relationships = []
                if isinstance(response, list):
                    for rel_data in response:
                        try:
                            rel = EntityRelationship(**rel_data)
                            relationships.append(rel)
                        except Exception as e:
                            logger.warning(f"Failed to parse relationship in chunk {chunk.metadata.chunk_id}: {e}")
                
                chunk_relationships.append(relationships)
                logger.info(f"Extracted {len(relationships)} relationships from chunk {chunk.metadata.chunk_id}")

                # Update previous entities for coreference resolution
                if enable_coreference_resolution:
                    chunk_entities = set()
                    for rel in relationships:
                        chunk_entities.add(rel.subject)
                        chunk_entities.add(rel.object)
                    previous_entities.extend(list(chunk_entities))
                    # Keep only unique entities and limit size to prevent prompt bloat
                    previous_entities = list(set(previous_entities))[-50:]  # Keep last 50 unique entities

            except Exception as e:
                logger.error(f"Failed to extract relationships from chunk {chunk.metadata.chunk_id}: {e}")
                chunk_relationships.append([])  # Empty list for failed chunk
        
        # Step 5: Build the graph structure
        logger.info("Building graph structure in Neo4j")
        graph_result = graph_builder.build_document_graph(file_id, chunks, chunk_relationships)
        
        # Step 6: Perform coreference resolution if enabled
        coreference_stats = None
        if enable_coreference_resolution and coreference_resolver:
            logger.info("Performing coreference resolution across chunks")
            # Collect entities by chunk for resolution
            entities_by_chunk = {}
            for i, relationships in enumerate(chunk_relationships):
                chunk_id = chunks[i].metadata.chunk_id
                entities = []
                for rel in relationships:
                    entities.append({"name": rel.subject, "entity_type": rel.subject_type})
                    entities.append({"name": rel.object, "entity_type": rel.object_type})
                entities_by_chunk[chunk_id] = entities

            # Resolve coreferences
            resolution_result = coreference_resolver.resolve_entities(entities_by_chunk)
            coreference_stats = resolution_result['statistics']
            logger.info(f"Coreference resolution completed: {coreference_stats}")

        # Step 7: Prepare result
        result = {
            "success": True,
            "file_id": file_id,
            "content_type": content_type,
            "coreference_resolution_enabled": enable_coreference_resolution,
            "coreference_statistics": coreference_stats,
            "chunks_created": len(chunks),
            "chunks_processed": graph_result.successful_chunks,
            "chunks_failed": graph_result.failed_chunks,
            "total_entities": graph_result.total_entities,
            "total_relationships": graph_result.total_relationships,
            "file_node_created": graph_result.file_node_created,
            "contains_relationships_created": graph_result.contains_relationships_created,
            "chunk_details": [
                {
                    "chunk_id": result.chunk_id,
                    "chunk_index": result.chunk_index,
                    "entities_extracted": result.entities_extracted,
                    "relationships_extracted": result.relationships_extracted,
                    "graph_stored": result.graph_stored,
                    "error": result.error
                }
                for result in graph_result.chunk_results
            ]
        }
        
        logger.info(f"Document processing completed: {graph_result.successful_chunks}/{len(chunks)} chunks processed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Failed to process document {file_id}: {e}")
        return {
            "success": False,
            "error": str(e),
            "file_id": file_id,
            "chunks_created": 0
        }
    
    finally:
        # Clean up connections
        try:
            neo4j_client.close()
        except:
            pass
