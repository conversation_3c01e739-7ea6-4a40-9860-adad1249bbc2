"""
Sample Customer Feedback Data for Testing Classification

This module contains diverse customer feedback samples for testing
the sentiment analysis and entity extraction capabilities of the
enterprise_kg_minimal system.
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
import random

# Sample customer feedback data with mixed positive and negative sentiment
CUSTOMER_FEEDBACK_SAMPLES = [
    # Positive Feedback Examples
    {
        "feedback_id": "FB001",
        "customer_name": "<PERSON>",
        "product_service": "CRM Software",
        "feedback_text": """
        I've been using your CRM software for the past 6 months and I'm extremely satisfied with the results. 
        The user interface is intuitive and our sales team productivity has increased by 40%. 
        The customer support team, especially <PERSON>, has been incredibly helpful whenever we had questions. 
        The reporting features are excellent and help us track our sales pipeline effectively. 
        I would definitely recommend this product to other businesses.
        """,
        "expected_sentiment": "positive",
        "expected_score": 0.8,
        "feedback_type": "compliment",
        "priority_level": "low"
    },
    
    {
        "feedback_id": "FB002", 
        "customer_name": "<PERSON>",
        "product_service": "Cloud Hosting Service",
        "feedback_text": """
        Outstanding service! Our website uptime has been 99.9% since we switched to your cloud hosting platform. 
        The migration process was smooth and the technical team guided us through every step. 
        The performance improvements are remarkable - our page load times decreased by 60%. 
        <PERSON> from the account management team has been proactive in ensuring our needs are met. 
        Great value for money!
        """,
        "expected_sentiment": "positive",
        "expected_score": 0.9,
        "feedback_type": "compliment",
        "priority_level": "low"
    },

    {
        "feedback_id": "FB003",
        "customer_name": "Lisa Chen",
        "product_service": "Mobile App",
        "feedback_text": """
        Love the new mobile app update! The user experience is much better now. 
        The navigation is smoother and the new features like offline mode are exactly what we needed. 
        My team can now access customer data even when traveling. 
        The push notifications help us stay on top of important updates. 
        Keep up the excellent work!
        """,
        "expected_sentiment": "positive",
        "expected_score": 0.7,
        "feedback_type": "compliment",
        "priority_level": "low"
    },

    # Negative Feedback Examples
    {
        "feedback_id": "FB004",
        "customer_name": "Robert Thompson",
        "product_service": "E-commerce Platform",
        "feedback_text": """
        I'm very disappointed with the e-commerce platform. We've been experiencing frequent crashes during peak hours, 
        which is costing us significant revenue. The checkout process is buggy and customers are abandoning their carts. 
        We've reported these issues multiple times but the response from support has been slow. 
        The promised features from the sales demo are still not working properly after 3 months. 
        This is seriously impacting our business operations.
        """,
        "expected_sentiment": "negative",
        "expected_score": -0.8,
        "feedback_type": "complaint",
        "priority_level": "high"
    },

    {
        "feedback_id": "FB005",
        "customer_name": "Maria Garcia",
        "product_service": "Customer Support",
        "feedback_text": """
        Terrible customer service experience. I've been trying to resolve a billing issue for over two weeks. 
        The support agents seem undertrained and keep transferring me between departments. 
        Nobody takes ownership of the problem. The wait times are excessive - sometimes over an hour. 
        This is completely unacceptable for a premium service. 
        I'm considering switching to a competitor if this doesn't improve immediately.
        """,
        "expected_sentiment": "negative",
        "expected_score": -0.9,
        "feedback_type": "complaint",
        "priority_level": "critical"
    },

    {
        "feedback_id": "FB006",
        "customer_name": "James Wilson",
        "product_service": "Data Analytics Tool",
        "feedback_text": """
        The data analytics tool is not meeting our expectations. The reports are often inaccurate and 
        the data visualization options are limited compared to what was promised. 
        The integration with our existing systems has been problematic. 
        We're spending more time fixing data issues than actually analyzing insights. 
        The training materials are outdated and don't reflect the current interface.
        """,
        "expected_sentiment": "negative",
        "expected_score": -0.6,
        "feedback_type": "complaint",
        "priority_level": "medium"
    },

    # Mixed/Neutral Feedback Examples
    {
        "feedback_id": "FB007",
        "customer_name": "Emily Davis",
        "product_service": "Project Management Software",
        "feedback_text": """
        The project management software has some good features but also several limitations. 
        The task tracking functionality works well and helps our team stay organized. 
        However, the reporting module is quite basic and doesn't provide the detailed analytics we need. 
        The user interface could be more modern and intuitive. 
        Customer support is responsive but sometimes lacks technical depth. 
        Overall, it's an okay solution but there's room for improvement.
        """,
        "expected_sentiment": "mixed",
        "expected_score": 0.1,
        "feedback_type": "suggestion",
        "priority_level": "medium"
    },

    {
        "feedback_id": "FB008",
        "customer_name": "Michael Brown",
        "product_service": "Security Software",
        "feedback_text": """
        The security software provides adequate protection for our network. 
        The threat detection capabilities are standard and work as expected. 
        The installation process was straightforward and the documentation is clear. 
        However, the software occasionally generates false positives which can be disruptive. 
        The price point is competitive compared to other solutions in the market. 
        It's a decent product that meets our basic security requirements.
        """,
        "expected_sentiment": "neutral",
        "expected_score": 0.0,
        "feedback_type": "review",
        "priority_level": "low"
    },

    # Complex feedback with multiple aspects
    {
        "feedback_id": "FB009",
        "customer_name": "Amanda Taylor",
        "product_service": "Enterprise Software Suite",
        "feedback_text": """
        Our experience with the enterprise software suite has been mixed. 
        The accounting module is excellent - it's streamlined our financial processes significantly. 
        Sarah from the implementation team did a fantastic job with the setup. 
        However, the HR module has been problematic with frequent sync issues. 
        The inventory management component works well but the user interface is outdated. 
        Customer support quality varies - some agents are very knowledgeable while others seem new. 
        The recent price increase was unexpected and not well communicated. 
        Overall, it's a powerful solution but needs more consistency across modules.
        """,
        "expected_sentiment": "mixed",
        "expected_score": 0.2,
        "feedback_type": "review",
        "priority_level": "medium"
    },

    {
        "feedback_id": "FB010",
        "customer_name": "Kevin Martinez",
        "product_service": "API Service",
        "feedback_text": """
        The API service has been critical for our application integration needs. 
        The documentation is comprehensive and the endpoints are well-designed. 
        Response times are generally good, though we've noticed some latency during peak hours. 
        The authentication system is secure and easy to implement. 
        However, the rate limiting could be more flexible for enterprise customers. 
        The developer portal is user-friendly and the code examples are helpful. 
        Technical support from the API team is knowledgeable and responsive. 
        It's a solid service that enables our business operations effectively.
        """,
        "expected_sentiment": "positive",
        "expected_score": 0.6,
        "feedback_type": "review",
        "priority_level": "low"
    }
]

def get_sample_feedback_data() -> List[Dict[str, Any]]:
    """Get all sample feedback data."""
    return CUSTOMER_FEEDBACK_SAMPLES

def get_positive_feedback_samples() -> List[Dict[str, Any]]:
    """Get only positive feedback samples."""
    return [fb for fb in CUSTOMER_FEEDBACK_SAMPLES if fb["expected_sentiment"] == "positive"]

def get_negative_feedback_samples() -> List[Dict[str, Any]]:
    """Get only negative feedback samples."""
    return [fb for fb in CUSTOMER_FEEDBACK_SAMPLES if fb["expected_sentiment"] == "negative"]

def get_mixed_neutral_feedback_samples() -> List[Dict[str, Any]]:
    """Get mixed and neutral feedback samples."""
    return [fb for fb in CUSTOMER_FEEDBACK_SAMPLES if fb["expected_sentiment"] in ["mixed", "neutral"]]

def get_high_priority_feedback() -> List[Dict[str, Any]]:
    """Get high priority feedback that requires immediate attention."""
    return [fb for fb in CUSTOMER_FEEDBACK_SAMPLES if fb["priority_level"] in ["high", "critical"]]

def generate_feedback_file_content(feedback_data: Dict[str, Any]) -> str:
    """Generate file content for a feedback sample."""
    return f"""
Customer Feedback Report

Feedback ID: {feedback_data['feedback_id']}
Customer: {feedback_data['customer_name']}
Product/Service: {feedback_data['product_service']}
Date: {datetime.now().strftime('%Y-%m-%d')}
Type: {feedback_data['feedback_type'].title()}
Priority: {feedback_data['priority_level'].title()}

Feedback Content:
{feedback_data['feedback_text'].strip()}

---
This feedback was submitted through our customer portal and requires appropriate follow-up action.
"""
