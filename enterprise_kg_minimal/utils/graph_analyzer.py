"""
Graph Connectivity Analyzer for Enterprise KG

This module provides functions to analyze the connectivity of knowledge graphs
stored in Neo4j, including checking if the graph is connected and providing
detailed connectivity statistics.
"""

import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass
from ..storage.neo4j_client import Neo4jClient

logger = logging.getLogger(__name__)


@dataclass
class ConnectivityResult:
    """Result of graph connectivity analysis."""
    is_connected: bool
    total_nodes: int
    total_relationships: int
    connected_components: int
    largest_component_size: int
    isolated_nodes: int
    connectivity_ratio: float
    component_details: List[Dict[str, Any]]
    file_chunk_connectivity: Dict[str, Any]


@dataclass
class ComponentInfo:
    """Information about a connected component."""
    component_id: int
    size: int
    nodes: List[str]
    node_types: Dict[str, int]
    relationship_types: Dict[str, int]


class GraphConnectivityAnalyzer:
    """
    Analyzer for checking knowledge graph connectivity.
    
    This class provides methods to analyze the connectivity of knowledge graphs
    created by the enterprise_kg_minimal package, including:
    - Overall graph connectivity
    - Connected components analysis
    - File-chunk-entity connectivity
    - Isolated node detection
    """
    
    def __init__(self, neo4j_client: Neo4jClient):
        """
        Initialize the connectivity analyzer.
        
        Args:
            neo4j_client: Neo4j client for database operations
        """
        self.neo4j_client = neo4j_client
    
    def analyze_graph_connectivity(
        self, 
        file_id: Optional[str] = None,
        include_file_chunk_structure: bool = True
    ) -> ConnectivityResult:
        """
        Perform comprehensive connectivity analysis of the knowledge graph.
        
        Args:
            file_id: Optional file ID to analyze specific document graph
            include_file_chunk_structure: Whether to include File-Chunk relationships
            
        Returns:
            ConnectivityResult with detailed analysis
        """
        logger.info(f"Starting connectivity analysis for file_id: {file_id}")
        
        # Get all nodes and relationships
        nodes = self._get_all_nodes(file_id)
        relationships = self._get_all_relationships(file_id, include_file_chunk_structure)
        
        if not nodes:
            return ConnectivityResult(
                is_connected=False,
                total_nodes=0,
                total_relationships=0,
                connected_components=0,
                largest_component_size=0,
                isolated_nodes=0,
                connectivity_ratio=0.0,
                component_details=[],
                file_chunk_connectivity={}
            )
        
        # Build adjacency list for connectivity analysis
        adjacency = self._build_adjacency_list(nodes, relationships)
        
        # Find connected components
        components = self._find_connected_components(adjacency)
        
        # Analyze file-chunk connectivity if requested
        file_chunk_connectivity = {}
        if include_file_chunk_structure:
            file_chunk_connectivity = self._analyze_file_chunk_connectivity(file_id)
        
        # Calculate statistics
        total_nodes = len(nodes)
        total_relationships = len(relationships)
        connected_components = len(components)
        largest_component_size = max(len(comp.nodes) for comp in components) if components else 0
        isolated_nodes = sum(1 for comp in components if len(comp.nodes) == 1)
        connectivity_ratio = largest_component_size / total_nodes if total_nodes > 0 else 0.0
        is_connected = connected_components == 1 and total_nodes > 0
        
        # Prepare component details
        component_details = [
            {
                "component_id": comp.component_id,
                "size": comp.size,
                "nodes": comp.nodes[:10],  # Limit to first 10 nodes for readability
                "node_types": comp.node_types,
                "relationship_types": comp.relationship_types,
                "is_isolated": comp.size == 1
            }
            for comp in components
        ]
        
        result = ConnectivityResult(
            is_connected=is_connected,
            total_nodes=total_nodes,
            total_relationships=total_relationships,
            connected_components=connected_components,
            largest_component_size=largest_component_size,
            isolated_nodes=isolated_nodes,
            connectivity_ratio=connectivity_ratio,
            component_details=component_details,
            file_chunk_connectivity=file_chunk_connectivity
        )
        
        logger.info(f"Connectivity analysis complete: {connected_components} components, "
                   f"largest: {largest_component_size}, connectivity: {connectivity_ratio:.2%}")
        
        return result

    def _get_all_nodes(self, file_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get all nodes in the graph, optionally filtered by file_id."""
        driver = self.neo4j_client._get_driver()

        with driver.session(database=self.neo4j_client.connection.database) as session:
            if file_id:
                # Get nodes related to specific file - include all entity types, not just Entity label
                query = """
                MATCH (f:File {id: $file_id})-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e)
                WHERE NOT e:File AND NOT e:Chunk
                RETURN DISTINCT e.name as name, labels(e) as labels, e as properties
                UNION
                MATCH (f:File {id: $file_id})-[:CONTAINS]->(c:Chunk)
                RETURN DISTINCT c.id as name, labels(c) as labels, c as properties
                UNION
                MATCH (f:File {id: $file_id})
                RETURN DISTINCT f.id as name, labels(f) as labels, f as properties
                """
                result = session.run(query, file_id=file_id)
            else:
                # Get all nodes
                query = """
                MATCH (n)
                RETURN DISTINCT
                    CASE
                        WHEN n.name IS NOT NULL THEN n.name
                        WHEN n.id IS NOT NULL THEN n.id
                        ELSE toString(id(n))
                    END as name,
                    labels(n) as labels,
                    n as properties
                """
                result = session.run(query)

            nodes = []
            for record in result:
                nodes.append({
                    "name": record["name"],
                    "labels": record["labels"],
                    "properties": dict(record["properties"])
                })

            return nodes

    def _get_all_relationships(
        self,
        file_id: Optional[str] = None,
        include_file_chunk_structure: bool = True
    ) -> List[Dict[str, Any]]:
        """Get all relationships in the graph."""
        driver = self.neo4j_client._get_driver()

        with driver.session(database=self.neo4j_client.connection.database) as session:
            if file_id:
                if include_file_chunk_structure:
                    # Include File-Chunk-Entity structure - match all entity types
                    query = """
                    MATCH (f:File {id: $file_id})-[r1:CONTAINS]->(c:Chunk)-[r2:EXTRACTED_FROM]->(e)
                    WHERE NOT e:File AND NOT e:Chunk
                    RETURN
                        CASE WHEN f.name IS NOT NULL THEN f.name ELSE f.id END as source,
                        CASE WHEN c.name IS NOT NULL THEN c.name ELSE c.id END as target,
                        type(r1) as rel_type
                    UNION
                    MATCH (f:File {id: $file_id})-[:CONTAINS]->(c:Chunk)-[r2:EXTRACTED_FROM]->(e)
                    WHERE NOT e:File AND NOT e:Chunk
                    RETURN
                        CASE WHEN c.name IS NOT NULL THEN c.name ELSE c.id END as source,
                        CASE WHEN e.name IS NOT NULL THEN e.name ELSE toString(elementId(e)) END as target,
                        type(r2) as rel_type
                    UNION
                    MATCH (f:File {id: $file_id})-[:CONTAINS]->(:Chunk)-[:EXTRACTED_FROM]->(e1)-[r]->(e2)
                    WHERE NOT e1:File AND NOT e1:Chunk AND NOT e2:File AND NOT e2:Chunk
                    AND type(r) <> 'EXTRACTED_FROM'
                    RETURN e1.name as source, e2.name as target, type(r) as rel_type
                    """
                else:
                    # Only entity relationships - match all entity types
                    query = """
                    MATCH (f:File {id: $file_id})-[:CONTAINS]->(:Chunk)-[:EXTRACTED_FROM]->(e1)-[r]->(e2)
                    WHERE NOT e1:File AND NOT e1:Chunk AND NOT e2:File AND NOT e2:Chunk
                    AND type(r) <> 'EXTRACTED_FROM'
                    RETURN e1.name as source, e2.name as target, type(r) as rel_type
                    """
                result = session.run(query, file_id=file_id)
            else:
                # Get all relationships
                if include_file_chunk_structure:
                    query = """
                    MATCH (n1)-[r]->(n2)
                    RETURN
                        CASE
                            WHEN n1.name IS NOT NULL THEN n1.name
                            WHEN n1.id IS NOT NULL THEN n1.id
                            ELSE toString(id(n1))
                        END as source,
                        CASE
                            WHEN n2.name IS NOT NULL THEN n2.name
                            WHEN n2.id IS NOT NULL THEN n2.id
                            ELSE toString(id(n2))
                        END as target,
                        type(r) as rel_type
                    """
                else:
                    query = """
                    MATCH (e1:Entity)-[r]->(e2:Entity)
                    WHERE type(r) <> 'EXTRACTED_FROM'
                    RETURN e1.name as source, e2.name as target, type(r) as rel_type
                    """
                result = session.run(query)

            relationships = []
            for record in result:
                relationships.append({
                    "source": record["source"],
                    "target": record["target"],
                    "type": record["rel_type"]
                })

            return relationships

    def _build_adjacency_list(
        self,
        nodes: List[Dict[str, Any]],
        relationships: List[Dict[str, Any]]
    ) -> Dict[str, Set[str]]:
        """Build adjacency list for connectivity analysis."""
        adjacency = {node["name"]: set() for node in nodes}

        for rel in relationships:
            source = rel["source"]
            target = rel["target"]

            # Add both directions for undirected connectivity analysis
            if source in adjacency:
                adjacency[source].add(target)
            if target in adjacency:
                adjacency[target].add(source)

        return adjacency

    def _find_connected_components(
        self,
        adjacency: Dict[str, Set[str]]
    ) -> List[ComponentInfo]:
        """Find connected components using DFS."""
        visited = set()
        components = []
        component_id = 0

        for node in adjacency:
            if node not in visited:
                # Start new component
                component_nodes = []
                self._dfs(node, adjacency, visited, component_nodes)

                if component_nodes:
                    # Analyze component
                    component_info = self._analyze_component(
                        component_id, component_nodes, adjacency
                    )
                    components.append(component_info)
                    component_id += 1

        return components

    def _dfs(
        self,
        node: str,
        adjacency: Dict[str, Set[str]],
        visited: Set[str],
        component_nodes: List[str]
    ):
        """Depth-first search for connected components."""
        visited.add(node)
        component_nodes.append(node)

        for neighbor in adjacency.get(node, set()):
            if neighbor not in visited:
                self._dfs(neighbor, adjacency, visited, component_nodes)

    def _analyze_component(
        self,
        component_id: int,
        nodes: List[str],
        adjacency: Dict[str, Set[str]]
    ) -> ComponentInfo:
        """Analyze a connected component."""
        # Get node types (simplified - would need actual node data)
        node_types = {"Entity": 0, "Chunk": 0, "File": 0, "Unknown": 0}
        relationship_types = {}

        for node in nodes:
            # Simple heuristic for node type detection
            if "_chunk_" in node:
                node_types["Chunk"] += 1
            elif any(keyword in node.lower() for keyword in ["file", "doc", "document"]):
                node_types["File"] += 1
            else:
                node_types["Entity"] += 1

        # Count relationship types within component
        for node in nodes:
            for neighbor in adjacency.get(node, set()):
                if neighbor in nodes:  # Only count internal relationships
                    # This is simplified - would need actual relationship data
                    rel_type = "UNKNOWN"
                    relationship_types[rel_type] = relationship_types.get(rel_type, 0) + 1

        return ComponentInfo(
            component_id=component_id,
            size=len(nodes),
            nodes=nodes,
            node_types=node_types,
            relationship_types=relationship_types
        )

    def _analyze_file_chunk_connectivity(self, file_id: Optional[str] = None) -> Dict[str, Any]:
        """Analyze the File-Chunk-Entity connectivity structure."""
        driver = self.neo4j_client._get_driver()

        with driver.session(database=self.neo4j_client.connection.database) as session:
            if file_id:
                # Analyze specific file - include all entity types
                query = """
                MATCH (f:File {id: $file_id})
                OPTIONAL MATCH (f)-[:CONTAINS]->(c:Chunk)
                OPTIONAL MATCH (c)-[:EXTRACTED_FROM]->(e)
                WHERE e IS NULL OR (NOT e:File AND NOT e:Chunk)
                RETURN
                    f.id as file_id,
                    count(DISTINCT c) as chunk_count,
                    count(DISTINCT e) as entity_count,
                    collect(DISTINCT c.id) as chunk_ids
                """
                result = session.run(query, file_id=file_id)
                record = result.single()

                if record:
                    return {
                        "file_id": record["file_id"],
                        "chunk_count": record["chunk_count"],
                        "entity_count": record["entity_count"],
                        "chunk_ids": record["chunk_ids"],
                        "has_file_chunk_structure": record["chunk_count"] > 0,
                        "has_chunk_entity_structure": record["entity_count"] > 0
                    }
            else:
                # Analyze all files
                query = """
                MATCH (f:File)
                OPTIONAL MATCH (f)-[:CONTAINS]->(c:Chunk)
                OPTIONAL MATCH (c)-[:EXTRACTED_FROM]->(e:Entity)
                RETURN
                    count(DISTINCT f) as file_count,
                    count(DISTINCT c) as total_chunk_count,
                    count(DISTINCT e) as total_entity_count
                """
                result = session.run(query)
                record = result.single()

                if record:
                    return {
                        "file_count": record["file_count"],
                        "total_chunk_count": record["total_chunk_count"],
                        "total_entity_count": record["total_entity_count"],
                        "has_file_chunk_structure": record["total_chunk_count"] > 0,
                        "has_chunk_entity_structure": record["total_entity_count"] > 0
                    }

        return {}

    def print_connectivity_report(self, result: ConnectivityResult, file_id: Optional[str] = None):
        """Print a detailed connectivity report."""
        print("\n" + "="*60)
        print("🔗 KNOWLEDGE GRAPH CONNECTIVITY ANALYSIS")
        print("="*60)

        if file_id:
            print(f"📄 File: {file_id}")
        else:
            print("📊 Global Graph Analysis")

        print(f"\n📈 Overall Statistics:")
        print(f"   Total Nodes: {result.total_nodes}")
        print(f"   Total Relationships: {result.total_relationships}")
        print(f"   Connected Components: {result.connected_components}")
        print(f"   Largest Component Size: {result.largest_component_size}")
        print(f"   Isolated Nodes: {result.isolated_nodes}")
        print(f"   Connectivity Ratio: {result.connectivity_ratio:.2%}")

        # Connectivity status
        if result.is_connected:
            print(f"\n✅ GRAPH IS CONNECTED")
            print("   All nodes are reachable from any other node")
        else:
            print(f"\n❌ GRAPH IS NOT CONNECTED")
            print(f"   Graph has {result.connected_components} separate components")

        # Component details
        if result.connected_components > 1:
            print(f"\n🧩 Component Details:")
            for comp in result.component_details[:5]:  # Show first 5 components
                status = "🔗" if comp["size"] > 1 else "🏝️"
                print(f"   {status} Component {comp['component_id']}: {comp['size']} nodes")
                if comp["size"] <= 5:  # Show nodes for small components
                    print(f"      Nodes: {', '.join(comp['nodes'])}")

        # File-chunk structure
        if result.file_chunk_connectivity:
            print(f"\n📁 File-Chunk Structure:")
            fc = result.file_chunk_connectivity
            if "file_id" in fc:
                print(f"   File: {fc['file_id']}")
                print(f"   Chunks: {fc['chunk_count']}")
                print(f"   Entities: {fc['entity_count']}")
                print(f"   Structure Complete: {fc['has_file_chunk_structure'] and fc['has_chunk_entity_structure']}")
            else:
                print(f"   Files: {fc.get('file_count', 0)}")
                print(f"   Total Chunks: {fc.get('total_chunk_count', 0)}")
                print(f"   Total Entities: {fc.get('total_entity_count', 0)}")

        print("="*60)
