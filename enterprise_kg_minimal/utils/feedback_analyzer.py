"""
Customer Feedback Analysis Utilities

This module provides utilities for analyzing customer feedback data
extracted by the enterprise_kg_minimal system, including sentiment
aggregation, customer satisfaction metrics, and feedback insights.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class FeedbackAnalyzer:
    """Analyzer for customer feedback data and sentiment insights."""
    
    def __init__(self, neo4j_client=None):
        """Initialize the feedback analyzer."""
        self.neo4j_client = neo4j_client
        
    def analyze_sentiment_distribution(self, feedback_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze sentiment distribution across feedback samples.
        
        Args:
            feedback_results: List of feedback processing results
            
        Returns:
            Dictionary with sentiment distribution analysis
        """
        sentiment_counts = Counter()
        sentiment_scores = []
        priority_distribution = Counter()
        
        for result in feedback_results:
            # Extract sentiment from entities or relationships
            sentiment_info = self._extract_sentiment_from_result(result)
            
            if sentiment_info:
                sentiment_counts[sentiment_info['sentiment']] += 1
                if sentiment_info['score'] is not None:
                    sentiment_scores.append(sentiment_info['score'])
                    
            # Count priority levels
            priority = self._extract_priority_from_result(result)
            if priority:
                priority_distribution[priority] += 1
        
        # Calculate statistics
        total_feedback = len(feedback_results)
        avg_sentiment_score = sum(sentiment_scores) / len(sentiment_scores) if sentiment_scores else 0.0
        
        return {
            "total_feedback_count": total_feedback,
            "sentiment_distribution": dict(sentiment_counts),
            "sentiment_percentages": {
                sentiment: (count / total_feedback * 100) 
                for sentiment, count in sentiment_counts.items()
            },
            "average_sentiment_score": avg_sentiment_score,
            "priority_distribution": dict(priority_distribution),
            "positive_feedback_ratio": sentiment_counts.get('positive', 0) / total_feedback if total_feedback > 0 else 0,
            "negative_feedback_ratio": sentiment_counts.get('negative', 0) / total_feedback if total_feedback > 0 else 0,
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def identify_common_issues(self, feedback_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Identify common issues and complaints from feedback data.
        
        Args:
            feedback_results: List of feedback processing results
            
        Returns:
            Dictionary with common issues analysis
        """
        complaint_keywords = defaultdict(int)
        product_issues = defaultdict(list)
        customer_complaints = []
        
        for result in feedback_results:
            # Extract complaint-related entities and relationships
            complaints = self._extract_complaints_from_result(result)
            
            for complaint in complaints:
                customer_complaints.append(complaint)
                
                # Count keywords in complaint text
                if 'text' in complaint:
                    words = complaint['text'].lower().split()
                    for word in words:
                        if len(word) > 3:  # Filter short words
                            complaint_keywords[word] += 1
                
                # Group by product/service
                if 'product' in complaint:
                    product_issues[complaint['product']].append(complaint)
        
        # Find most common issues
        top_keywords = dict(sorted(complaint_keywords.items(), key=lambda x: x[1], reverse=True)[:20])
        
        return {
            "total_complaints": len(customer_complaints),
            "top_complaint_keywords": top_keywords,
            "product_issue_breakdown": {
                product: len(issues) for product, issues in product_issues.items()
            },
            "detailed_product_issues": dict(product_issues),
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def analyze_customer_satisfaction_trends(self, feedback_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze customer satisfaction trends and patterns.
        
        Args:
            feedback_results: List of feedback processing results
            
        Returns:
            Dictionary with satisfaction trend analysis
        """
        customer_scores = defaultdict(list)
        product_scores = defaultdict(list)
        time_series_data = []
        
        for result in feedback_results:
            # Extract customer and product satisfaction data
            satisfaction_data = self._extract_satisfaction_from_result(result)
            
            if satisfaction_data:
                customer = satisfaction_data.get('customer')
                product = satisfaction_data.get('product')
                score = satisfaction_data.get('score')
                timestamp = satisfaction_data.get('timestamp', datetime.now())
                
                if customer and score is not None:
                    customer_scores[customer].append(score)
                    
                if product and score is not None:
                    product_scores[product].append(score)
                    
                time_series_data.append({
                    'timestamp': timestamp,
                    'score': score,
                    'customer': customer,
                    'product': product
                })
        
        # Calculate averages and trends
        customer_avg_scores = {
            customer: sum(scores) / len(scores) 
            for customer, scores in customer_scores.items()
        }
        
        product_avg_scores = {
            product: sum(scores) / len(scores) 
            for product, scores in product_scores.items()
        }
        
        return {
            "customer_satisfaction_averages": customer_avg_scores,
            "product_satisfaction_averages": product_avg_scores,
            "highest_rated_products": sorted(
                product_avg_scores.items(), key=lambda x: x[1], reverse=True
            )[:5],
            "lowest_rated_products": sorted(
                product_avg_scores.items(), key=lambda x: x[1]
            )[:5],
            "most_satisfied_customers": sorted(
                customer_avg_scores.items(), key=lambda x: x[1], reverse=True
            )[:10],
            "time_series_data": time_series_data,
            "analysis_timestamp": datetime.now().isoformat()
        }
    
    def generate_action_recommendations(self, feedback_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Generate actionable recommendations based on feedback analysis.
        
        Args:
            feedback_analysis: Combined feedback analysis results
            
        Returns:
            List of action recommendations
        """
        recommendations = []
        
        # Analyze sentiment distribution
        sentiment_dist = feedback_analysis.get('sentiment_distribution', {})
        negative_ratio = feedback_analysis.get('negative_feedback_ratio', 0)
        
        if negative_ratio > 0.3:  # More than 30% negative feedback
            recommendations.append({
                "priority": "high",
                "category": "customer_satisfaction",
                "title": "Address High Negative Feedback Rate",
                "description": f"Negative feedback rate is {negative_ratio:.1%}, which is above acceptable threshold",
                "action_items": [
                    "Investigate root causes of customer dissatisfaction",
                    "Implement immediate fixes for common issues",
                    "Enhance customer support response times",
                    "Review product quality and service delivery"
                ]
            })
        
        # Analyze common issues
        common_issues = feedback_analysis.get('top_complaint_keywords', {})
        if common_issues:
            top_issue = max(common_issues.items(), key=lambda x: x[1])
            recommendations.append({
                "priority": "medium",
                "category": "product_improvement",
                "title": f"Address Common Issue: {top_issue[0].title()}",
                "description": f"'{top_issue[0]}' appears in {top_issue[1]} complaints",
                "action_items": [
                    f"Investigate issues related to '{top_issue[0]}'",
                    "Develop targeted solutions",
                    "Communicate fixes to affected customers",
                    "Monitor improvement in related feedback"
                ]
            })
        
        # Analyze product satisfaction
        product_scores = feedback_analysis.get('product_satisfaction_averages', {})
        if product_scores:
            lowest_rated = min(product_scores.items(), key=lambda x: x[1])
            if lowest_rated[1] < 0.0:  # Negative average score
                recommendations.append({
                    "priority": "high",
                    "category": "product_focus",
                    "title": f"Improve {lowest_rated[0]} Product",
                    "description": f"{lowest_rated[0]} has low satisfaction score: {lowest_rated[1]:.2f}",
                    "action_items": [
                        f"Conduct detailed review of {lowest_rated[0]}",
                        "Gather specific feedback from users",
                        "Develop improvement roadmap",
                        "Consider product redesign or feature updates"
                    ]
                })
        
        return recommendations
    
    def _extract_sentiment_from_result(self, result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract sentiment information from processing result."""
        # This would typically parse the extracted entities and relationships
        # to find sentiment-related information
        # For now, return a placeholder implementation
        return {
            "sentiment": "neutral",
            "score": 0.0,
            "confidence": 0.5
        }
    
    def _extract_priority_from_result(self, result: Dict[str, Any]) -> Optional[str]:
        """Extract priority level from processing result."""
        # Placeholder implementation
        return "medium"
    
    def _extract_complaints_from_result(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract complaint information from processing result."""
        # Placeholder implementation
        return []
    
    def _extract_satisfaction_from_result(self, result: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract satisfaction data from processing result."""
        # Placeholder implementation
        return {
            "customer": "Unknown Customer",
            "product": "Unknown Product",
            "score": 0.0,
            "timestamp": datetime.now()
        }


def analyze_feedback_batch(feedback_results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyze a batch of feedback results and generate comprehensive insights.
    
    Args:
        feedback_results: List of feedback processing results
        
    Returns:
        Comprehensive analysis dictionary
    """
    analyzer = FeedbackAnalyzer()
    
    # Perform various analyses
    sentiment_analysis = analyzer.analyze_sentiment_distribution(feedback_results)
    issues_analysis = analyzer.identify_common_issues(feedback_results)
    satisfaction_analysis = analyzer.analyze_customer_satisfaction_trends(feedback_results)
    
    # Combine all analyses
    combined_analysis = {
        "sentiment_analysis": sentiment_analysis,
        "issues_analysis": issues_analysis,
        "satisfaction_analysis": satisfaction_analysis,
        "summary": {
            "total_feedback_processed": len(feedback_results),
            "analysis_timestamp": datetime.now().isoformat(),
            "key_metrics": {
                "average_sentiment": sentiment_analysis.get('average_sentiment_score', 0.0),
                "negative_feedback_ratio": sentiment_analysis.get('negative_feedback_ratio', 0.0),
                "total_complaints": issues_analysis.get('total_complaints', 0),
                "products_analyzed": len(satisfaction_analysis.get('product_satisfaction_averages', {}))
            }
        }
    }
    
    # Generate recommendations
    recommendations = analyzer.generate_action_recommendations(combined_analysis)
    combined_analysis["recommendations"] = recommendations
    
    return combined_analysis


def generate_feedback_report(analysis_results: Dict[str, Any]) -> str:
    """
    Generate a human-readable feedback analysis report.
    
    Args:
        analysis_results: Results from analyze_feedback_batch
        
    Returns:
        Formatted report string
    """
    report = f"""
# Customer Feedback Analysis Report

**Generated:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**Total Feedback Processed:** {analysis_results['summary']['total_feedback_processed']}

## Executive Summary

- **Average Sentiment Score:** {analysis_results['summary']['key_metrics']['average_sentiment']:.2f}
- **Negative Feedback Ratio:** {analysis_results['summary']['key_metrics']['negative_feedback_ratio']:.1%}
- **Total Complaints:** {analysis_results['summary']['key_metrics']['total_complaints']}
- **Products Analyzed:** {analysis_results['summary']['key_metrics']['products_analyzed']}

## Sentiment Distribution

"""
    
    sentiment_dist = analysis_results['sentiment_analysis']['sentiment_percentages']
    for sentiment, percentage in sentiment_dist.items():
        report += f"- **{sentiment.title()}:** {percentage:.1f}%\n"
    
    report += "\n## Action Recommendations\n\n"
    
    for i, rec in enumerate(analysis_results.get('recommendations', []), 1):
        report += f"### {i}. {rec['title']} (Priority: {rec['priority'].title()})\n"
        report += f"{rec['description']}\n\n"
        report += "**Action Items:**\n"
        for item in rec['action_items']:
            report += f"- {item}\n"
        report += "\n"
    
    return report
