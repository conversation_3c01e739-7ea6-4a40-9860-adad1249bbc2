"""
LLM Client for Enterprise KG Minimal

This module provides a clean LLM client that can work with different providers
for entity and relationship extraction.
"""

import os
import json
import logging
import re
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class LLMClient:
    """
    LLM client that supports multiple providers for structured information extraction.
    """

    def __init__(self, provider: str = "openai", model: str = "gpt-4o", api_key: Optional[str] = None):
        """
        Initialize LLM client.

        Args:
            provider: LLM provider (openai, anthropic, gemini, openrouter, requesty)
            model: Model name
            api_key: API key (will try to get from environment if not provided)
        """
        self.provider = provider.lower()
        self.model = model
        self.api_key = api_key or self._get_api_key()
        self.client = self._initialize_client()

    def _get_api_key(self) -> Optional[str]:
        """Get API key from environment variables."""
        env_vars = {
            "openai": "OPENAI_API_KEY",
            "anthropic": "ANTHROPIC_API_KEY", 
            "gemini": "GEMINI_API_KEY",
            "openrouter": "OPENROUTER_API_KEY",
            "requesty": "REQUESTY_API_KEY"
        }
        return os.getenv(env_vars.get(self.provider))

    def _initialize_client(self):
        """Initialize the appropriate LLM client."""
        if self.provider == "openai":
            try:
                import openai
                return openai.OpenAI(api_key=self.api_key)
            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                return None
        elif self.provider == "anthropic":
            try:
                import anthropic
                return anthropic.Anthropic(api_key=self.api_key)
            except ImportError:
                logger.error("Anthropic package not installed. Install with: pip install anthropic")
                return None
        elif self.provider == "openrouter":
            try:
                import openai
                return openai.OpenAI(
                    api_key=self.api_key,
                    base_url="https://openrouter.ai/api/v1"
                )
            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                return None
        elif self.provider == "requesty":
            try:
                import openai
                base_url = os.getenv("REQUESTY_BASE_URL", "https://router.requesty.ai/v1")
                return openai.OpenAI(
                    api_key=self.api_key,
                    base_url=base_url
                )
            except ImportError:
                logger.error("OpenAI package not installed. Install with: pip install openai")
                return None
        else:
            logger.warning(f"Provider {self.provider} not implemented")
            return None

    def generate_structured_response(self, prompt: str, schema_description: str) -> Dict[str, Any]:
        """
        Generate a structured response from the LLM.

        Args:
            prompt: The input prompt
            schema_description: Description of the expected output schema

        Returns:
            Parsed JSON response
        """
        if not self.client:
            raise ValueError("LLM client not initialized")

        system_prompt = f"""
You are an expert at extracting structured information from enterprise documents.
You must respond with ONLY valid JSON that matches this schema - no explanatory text before or after:

{schema_description}

IMPORTANT: Return ONLY the JSON array/object. Do not include any explanatory text, comments, or formatting outside the JSON.
Be precise and only extract information that is clearly stated or strongly implied in the text.
"""

        try:
            if self.provider in ["openai", "openrouter", "requesty"]:
                response = self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1
                )
                content = response.choices[0].message.content
            elif self.provider == "anthropic":
                response = self.client.messages.create(
                    model=self.model,
                    max_tokens=4000,
                    system=system_prompt,
                    messages=[{"role": "user", "content": prompt}],
                    temperature=0.1
                )
                content = response.content[0].text
            else:
                raise ValueError(f"Provider {self.provider} not supported")

            return self._extract_json_from_response(content)

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.error(f"Raw response: {content}")
            return {}
        except Exception as e:
            logger.error(f"LLM API error: {e}")
            return {}

    def _extract_json_from_response(self, content: str) -> Dict[str, Any]:
        """Extract JSON from LLM response, handling cases where there might be extra text."""
        # First try to parse the content directly
        try:
            return json.loads(content.strip())
        except json.JSONDecodeError:
            pass

        # Try to find JSON array or object in the response
        json_patterns = [
            r'\[.*\]',  # JSON array
            r'\{.*\}',  # JSON object
        ]

        for pattern in json_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue

        # If no valid JSON found, return empty structure
        logger.warning("No valid JSON found in LLM response")
        return {}
