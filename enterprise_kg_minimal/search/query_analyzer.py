"""
Query Analyzer for Intelligent Search Routing

This module analyzes queries to determine their complexity and the optimal search strategy.
It helps decide whether graph traversal is necessary or if semantic search alone is sufficient.
"""

import re
import logging
from typing import List, Dict, Any, Set, Optional
from dataclasses import dataclass

from .search_schemas import (
    QueryComplexity, 
    QueryAnalysis, 
    RoutingDecision,
    VectorMetadata
)
from ..constants.entities import get_all_entity_types
from ..constants.relationships import get_all_relationship_types

logger = logging.getLogger(__name__)


class QueryAnalyzer:
    """
    Analyzes queries to determine complexity and routing decisions.
    """
    
    def __init__(self):
        """Initialize the query analyzer with pattern matching rules."""
        self.entity_types = get_all_entity_types()
        self.relationship_types = get_all_relationship_types()
        
        # Query pattern definitions
        self.factual_patterns = [
            r'\b(what is|what are|define|definition of|meaning of)\b',
            r'\b(when is|when was|when did|when does)\b',
            r'\b(where is|where are|where was|where were)\b',
            r'\b(who is|who are|who was|who were)\b',
            r'\b(how much|how many|how long|how often)\b',
            r'\b(list|show me|give me|find)\b.*\b(policy|procedure|rule|guideline)\b'
        ]
        
        self.relational_patterns = [
            r'\b(relationship|relation|connection|link)\b',
            r'\b(between|among|connects|relates to|associated with)\b',
            r'\b(hierarchy|structure|organization|reporting)\b',
            r'\b(depends on|influences|affects|impacts)\b',
            r'\b(compare|contrast|difference|similarity)\b',
            r'\b(network|graph|tree|chain)\b'
        ]
        
        self.hierarchical_patterns = [
            r'\b(manager|supervisor|director|ceo|cto|vp)\b',
            r'\b(department|team|division|unit|group)\b',
            r'\b(reports to|works under|managed by)\b',
            r'\b(organizational|org chart|structure)\b'
        ]
        
        # Simple query indicators (likely answerable by semantic search alone)
        self.simple_query_indicators = [
            r'\b(leave policy|vacation policy|holiday policy)\b',
            r'\b(contact|phone|email|address)\b',
            r'\b(definition|meaning|what is)\b',
            r'\b(procedure|process|steps|how to)\b',
            r'\b(deadline|due date|timeline)\b'
        ]
        
        logger.info("Query analyzer initialized with pattern matching rules")
    
    def analyze_query(
        self, 
        query_text: Optional[str], 
        vector_metadata: Optional[List[VectorMetadata]] = None
    ) -> QueryAnalysis:
        """
        Analyze a query to determine its complexity and routing needs.
        
        Args:
            query_text: The query text to analyze
            vector_metadata: Optional metadata from vector search results
            
        Returns:
            QueryAnalysis with routing recommendations
        """
        if not query_text:
            return self._create_default_analysis()
        
        query_lower = query_text.lower()
        
        # Detect query patterns
        is_factual = self._detect_factual_query(query_lower)
        is_relational = self._detect_relational_query(query_lower)
        is_hierarchical = self._detect_hierarchical_query(query_lower)
        is_simple = self._detect_simple_query(query_lower)
        
        # Extract entities and relationships
        detected_entities = self._extract_entity_mentions(query_lower)
        detected_relationships = self._extract_relationship_mentions(query_lower)
        query_keywords = self._extract_keywords(query_lower)
        
        # Calculate complexity score
        complexity_score = self._calculate_complexity_score(
            is_factual, is_relational, is_hierarchical, is_simple,
            detected_entities, detected_relationships
        )
        
        # Determine complexity level
        query_complexity = self._determine_complexity_level(complexity_score, is_simple)
        
        # Analyze metadata sufficiency
        metadata_analysis = self._analyze_metadata_sufficiency(
            query_text, vector_metadata
        ) if vector_metadata else (0.0, False)
        
        # Make routing recommendation
        routing_decision, confidence, reasoning = self._recommend_routing(
            query_complexity, 
            complexity_score,
            metadata_analysis[0],
            metadata_analysis[1],
            is_factual,
            is_relational,
            is_simple
        )
        
        return QueryAnalysis(
            query_complexity=query_complexity,
            complexity_score=complexity_score,
            is_factual_query=is_factual,
            is_relational_query=is_relational,
            is_hierarchical_query=is_hierarchical,
            has_entity_references=len(detected_entities) > 0,
            has_relationship_keywords=len(detected_relationships) > 0,
            metadata_sufficiency_score=metadata_analysis[0],
            metadata_contains_answer=metadata_analysis[1],
            recommended_decision=routing_decision,
            confidence_score=confidence,
            detected_entities=detected_entities,
            detected_relationships=detected_relationships,
            query_keywords=query_keywords,
            reasoning=reasoning
        )
    
    def _detect_factual_query(self, query_lower: str) -> bool:
        """Detect if query is asking for factual information."""
        return any(re.search(pattern, query_lower) for pattern in self.factual_patterns)
    
    def _detect_relational_query(self, query_lower: str) -> bool:
        """Detect if query is asking about relationships."""
        return any(re.search(pattern, query_lower) for pattern in self.relational_patterns)
    
    def _detect_hierarchical_query(self, query_lower: str) -> bool:
        """Detect if query is about organizational hierarchy."""
        return any(re.search(pattern, query_lower) for pattern in self.hierarchical_patterns)
    
    def _detect_simple_query(self, query_lower: str) -> bool:
        """Detect if query is simple and likely answerable by semantic search alone."""
        return any(re.search(pattern, query_lower) for pattern in self.simple_query_indicators)
    
    def _extract_entity_mentions(self, query_lower: str) -> List[str]:
        """Extract potential entity mentions from query."""
        detected = []
        for entity_type in self.entity_types:
            if entity_type.lower() in query_lower:
                detected.append(entity_type)
        return detected
    
    def _extract_relationship_mentions(self, query_lower: str) -> List[str]:
        """Extract potential relationship mentions from query."""
        detected = []
        for rel_type in self.relationship_types:
            # Convert relationship type to searchable patterns
            rel_patterns = [
                rel_type.lower(),
                rel_type.lower().replace('_', ' '),
                rel_type.lower().replace('_', '-')
            ]
            if any(pattern in query_lower for pattern in rel_patterns):
                detected.append(rel_type)
        return detected
    
    def _extract_keywords(self, query_lower: str) -> List[str]:
        """Extract important keywords from query."""
        # Simple keyword extraction - can be enhanced with NLP
        words = re.findall(r'\b\w+\b', query_lower)
        # Filter out common stop words
        stop_words = {'the', 'is', 'are', 'was', 'were', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        return keywords[:10]  # Limit to top 10 keywords
    
    def _calculate_complexity_score(
        self, 
        is_factual: bool, 
        is_relational: bool, 
        is_hierarchical: bool,
        is_simple: bool,
        detected_entities: List[str], 
        detected_relationships: List[str]
    ) -> float:
        """Calculate a complexity score from 0.0 to 1.0."""
        score = 0.0
        
        # Simple queries get low scores
        if is_simple:
            score += 0.1
        
        # Factual queries are moderately complex
        if is_factual:
            score += 0.3
        
        # Relational queries are more complex
        if is_relational:
            score += 0.5
        
        # Hierarchical queries are complex
        if is_hierarchical:
            score += 0.6
        
        # Entity and relationship mentions add complexity
        score += min(0.2, len(detected_entities) * 0.05)
        score += min(0.3, len(detected_relationships) * 0.1)
        
        return min(1.0, score)
    
    def _determine_complexity_level(self, complexity_score: float, is_simple: bool) -> QueryComplexity:
        """Determine complexity level based on score and patterns."""
        if is_simple or complexity_score < 0.3:
            return QueryComplexity.SIMPLE
        elif complexity_score < 0.6:
            return QueryComplexity.MODERATE
        else:
            return QueryComplexity.COMPLEX
    
    def _analyze_metadata_sufficiency(
        self, 
        query_text: str, 
        vector_metadata: List[VectorMetadata]
    ) -> tuple[float, bool]:
        """
        Analyze if vector metadata contains sufficient information to answer the query.
        
        Returns:
            Tuple of (sufficiency_score, contains_answer)
        """
        if not vector_metadata:
            return 0.0, False
        
        query_keywords = set(self._extract_keywords(query_text.lower()))
        total_score = 0.0
        contains_answer = False
        
        for metadata in vector_metadata:
            chunk_keywords = set(self._extract_keywords(metadata.chunk_text.lower()))
            
            # Calculate keyword overlap
            overlap = len(query_keywords & chunk_keywords)
            overlap_score = overlap / max(1, len(query_keywords))
            
            # Check if chunk seems to contain direct answer
            if self._chunk_contains_answer(query_text, metadata.chunk_text):
                contains_answer = True
                overlap_score += 0.3
            
            # Weight by similarity score
            weighted_score = overlap_score * metadata.similarity_score
            total_score += weighted_score
        
        # Average and normalize
        avg_score = total_score / len(vector_metadata)
        return min(1.0, avg_score), contains_answer
    
    def _chunk_contains_answer(self, query: str, chunk_text: str) -> bool:
        """Check if chunk likely contains answer to query."""
        query_lower = query.lower()
        chunk_lower = chunk_text.lower()
        
        # Simple heuristics - can be enhanced
        if any(pattern in query_lower for pattern in [r'\bwhat is\b', r'\bdefine\b']):
            return any(word in chunk_lower for word in ['definition', 'means', 'refers to', 'is defined as'])
        
        if any(pattern in query_lower for pattern in [r'\bhow to\b', r'\bprocedure\b']):
            return any(word in chunk_lower for word in ['steps', 'procedure', 'process', 'follow'])
        
        if 'policy' in query_lower:
            return 'policy' in chunk_lower
        
        return False
    
    def _recommend_routing(
        self,
        complexity: QueryComplexity,
        complexity_score: float,
        metadata_sufficiency: float,
        metadata_contains_answer: bool,
        is_factual: bool,
        is_relational: bool,
        is_simple: bool
    ) -> tuple[RoutingDecision, float, str]:
        """
        Recommend routing decision based on analysis.
        
        Returns:
            Tuple of (decision, confidence, reasoning)
        """
        reasoning_parts = []
        
        # High metadata sufficiency suggests semantic-only search
        if metadata_sufficiency > 0.7 and metadata_contains_answer:
            reasoning_parts.append(f"High metadata sufficiency ({metadata_sufficiency:.2f})")
            return RoutingDecision.SEMANTIC_ONLY, 0.9, "; ".join(reasoning_parts)
        
        # Simple queries with good metadata
        if is_simple and metadata_sufficiency > 0.5:
            reasoning_parts.append("Simple query with sufficient metadata")
            return RoutingDecision.SEMANTIC_ONLY, 0.8, "; ".join(reasoning_parts)
        
        # Complex relational queries need full graph
        if is_relational and complexity_score > 0.6:
            reasoning_parts.append("Complex relational query detected")
            return RoutingDecision.FULL_GRAPH, 0.9, "; ".join(reasoning_parts)
        
        # Moderate complexity might benefit from light graph expansion
        if complexity == QueryComplexity.MODERATE:
            reasoning_parts.append("Moderate complexity query")
            if metadata_sufficiency > 0.4:
                reasoning_parts.append("with decent metadata coverage")
                return RoutingDecision.LIGHT_GRAPH, 0.7, "; ".join(reasoning_parts)
            else:
                reasoning_parts.append("with low metadata coverage")
                return RoutingDecision.FULL_GRAPH, 0.6, "; ".join(reasoning_parts)
        
        # Default to adaptive for uncertain cases
        reasoning_parts.append("Uncertain case, using adaptive approach")
        return RoutingDecision.ADAPTIVE, 0.5, "; ".join(reasoning_parts)
    
    def _create_default_analysis(self) -> QueryAnalysis:
        """Create default analysis for empty queries."""
        return QueryAnalysis(
            query_complexity=QueryComplexity.SIMPLE,
            complexity_score=0.0,
            recommended_decision=RoutingDecision.SEMANTIC_ONLY,
            confidence_score=0.5,
            reasoning="No query text provided"
        )
