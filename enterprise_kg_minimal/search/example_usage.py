#!/usr/bin/env python3
"""
Example Usage for Enterprise KG Hybrid Search Engine

This script demonstrates how to use the hybrid search engine to combine
vector similarity search results (chunk indices) with graph-based retrieval.

The hybrid search engine is designed to work with chunk indices that would
typically come from a vector database like Pinecone after similarity search.
"""

import logging
from typing import List, Set, Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def example_basic_hybrid_search():
    """
    Example of basic hybrid search with chunk indices.
    
    This simulates the typical workflow where:
    1. User submits a query
    2. Vector similarity search returns chunk indices (from Pinecone)
    3. Hybrid search engine enriches results with graph context
    """
    print("🔍 Basic Hybrid Search Example")
    print("=" * 50)
    
    # Simulated chunk indices from vector similarity search (e.g., from Pinecone)
    chunk_indices = [
        "doc1_chunk_0_abc123",
        "doc1_chunk_1_def456", 
        "doc2_chunk_0_ghi789",
        "doc3_chunk_2_jkl012"
    ]
    
    query_text = "Who manages the AI project and what technologies are involved?"
    
    print(f"Query: {query_text}")
    print(f"Chunk indices from vector search: {chunk_indices}")
    print()
    
    try:
        from .hybrid_search_engine import search_with_chunk_indices
        from .search_schemas import SearchStrategy
        
        # Execute hybrid search
        result = search_with_chunk_indices(
            chunk_indices=chunk_indices,
            query_text=query_text,
            strategy=SearchStrategy.HYBRID,
            max_results=20,
            expansion_depth=2,
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="password"
        )
        
        # Display results
        print("📊 Search Results:")
        print(f"Total results: {result.total_results}")
        print(f"Processing time: {result.processing_time_ms:.2f}ms")
        print(f"Strategy used: {result.strategy_used.value}")
        print(f"Coverage score: {result.coverage_score:.2f}")
        print(f"Coherence score: {result.coherence_score:.2f}")
        print(f"Relevance score: {result.relevance_score:.2f}")
        print()
        
        # Display top entities
        print("🏢 Top Entities Found:")
        for i, entity in enumerate(result.get_top_entities(5), 1):
            print(f"{i}. {entity.name} ({entity.entity_type})")
            print(f"   Relevance: {entity.relevance_score:.2f}")
            print(f"   Reason: {entity.match_reason}")
            print(f"   Chunk sources: {len(entity.chunk_sources)}")
            print()
        
        # Display top relationships
        print("🔗 Top Relationships Found:")
        for i, rel in enumerate(result.get_top_relationships(5), 1):
            print(f"{i}. {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity}")
            print(f"   Confidence: {rel.confidence_score:.2f}")
            print(f"   Relevance: {rel.relevance_score:.2f}")
            if rel.context:
                print(f"   Context: {rel.context[:100]}...")
            print()
        
        # Display graph statistics
        print("📈 Graph Statistics:")
        print(f"Entity types found: {len(result.graph_context.entity_types_found)}")
        print(f"Relationship types found: {len(result.graph_context.relationship_types_found)}")
        print(f"Max expansion depth: {result.graph_context.max_depth_reached}")
        print(f"Source chunks: {result.graph_context.chunk_count}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure Neo4j is running and the database contains graph data.")


def example_filtered_search():
    """
    Example of hybrid search with entity and relationship type filters.
    """
    print("\n🎯 Filtered Search Example")
    print("=" * 50)
    
    # Simulated chunk indices
    chunk_indices = ["doc1_chunk_0_abc123", "doc1_chunk_1_def456"]
    
    # Filter for specific entity and relationship types
    entity_types = {"Person", "Manager", "Project", "Technology"}
    relationship_types = {"manages", "works_for", "involved_in", "uses"}
    
    print(f"Entity type filters: {entity_types}")
    print(f"Relationship type filters: {relationship_types}")
    print()
    
    try:
        from .hybrid_search_engine import search_with_chunk_indices
        from .search_schemas import SearchStrategy
        
        result = search_with_chunk_indices(
            chunk_indices=chunk_indices,
            query_text="organizational structure and technology usage",
            strategy=SearchStrategy.HIERARCHICAL,
            entity_types=entity_types,
            relationship_types=relationship_types,
            max_results=15,
            expansion_depth=3
        )
        
        print("📊 Filtered Search Results:")
        print(f"Entities found: {len(result.graph_context.entities)}")
        print(f"Relationships found: {len(result.graph_context.relationships)}")
        print(f"Entity types: {result.graph_context.entity_types_found}")
        print(f"Relationship types: {result.graph_context.relationship_types_found}")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def example_entity_neighborhood_search():
    """
    Example of searching for the neighborhood around a specific entity.
    """
    print("\n🌐 Entity Neighborhood Search Example")
    print("=" * 50)
    
    entity_name = "John Smith"  # Example entity name
    
    print(f"Finding neighborhood around entity: {entity_name}")
    print()
    
    try:
        from .hybrid_search_engine import create_hybrid_search_engine
        
        # Create search engine
        search_engine = create_hybrid_search_engine(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="password"
        )
        
        # Search entity neighborhood
        result = search_engine.search_entity_neighborhood(
            entity_name=entity_name,
            max_depth=2,
            entity_types={"Person", "Project", "Company", "Team"},
            max_results=20
        )
        
        print("📊 Neighborhood Results:")
        print(f"Connected entities: {len(result.graph_context.entities)}")
        print(f"Relationships: {len(result.graph_context.relationships)}")
        print()
        
        # Show connected entities
        print("🔗 Connected Entities:")
        for entity in result.graph_context.entities[:10]:
            print(f"- {entity.name} ({entity.entity_type})")
        
        # Show relationships
        print("\n🔗 Relationships:")
        for rel in result.graph_context.relationships[:10]:
            print(f"- {rel.source_entity} --[{rel.relationship_type}]--> {rel.target_entity}")
        
        # Clean up
        search_engine.neo4j_client.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")


def example_search_validation():
    """
    Example of validating search parameters before execution.
    """
    print("\n✅ Search Parameter Validation Example")
    print("=" * 50)
    
    try:
        from .hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine()
        
        # Get available types
        available_types = search_engine.get_available_types()
        print("📋 Available Types:")
        print(f"Entity types: {available_types['total_entity_types']}")
        print(f"Relationship types: {available_types['total_relationship_types']}")
        print(f"Entity categories: {available_types['entity_categories']}")
        print()
        
        # Validate search parameters
        chunk_indices = ["chunk1", "chunk2"]
        entity_types = {"Person", "InvalidType", "Company"}  # Mix of valid and invalid
        relationship_types = {"manages", "invalid_rel", "works_for"}
        
        validation = search_engine.validate_search_parameters(
            chunk_indices=chunk_indices,
            entity_types=entity_types,
            relationship_types=relationship_types,
            expansion_depth=2,
            max_results=50
        )
        
        print("🔍 Validation Results:")
        print(f"Valid: {validation['valid']}")
        if validation['warnings']:
            print("⚠️  Warnings:")
            for warning in validation['warnings']:
                print(f"  - {warning}")
        if validation['errors']:
            print("❌ Errors:")
            for error in validation['errors']:
                print(f"  - {error}")
        if validation['suggestions']:
            print("💡 Suggestions:")
            for suggestion in validation['suggestions']:
                print(f"  - {suggestion}")
        
        search_engine.neo4j_client.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")


def example_strategy_comparison():
    """
    Example comparing different search strategies.
    """
    print("\n⚖️  Strategy Comparison Example")
    print("=" * 50)
    
    chunk_indices = ["doc1_chunk_0_abc123", "doc1_chunk_1_def456"]
    
    try:
        from .hybrid_search_engine import search_with_chunk_indices
        from .search_schemas import SearchStrategy
        
        strategies = [
            SearchStrategy.ENTITY_CENTRIC,
            SearchStrategy.RELATIONSHIP_CENTRIC,
            SearchStrategy.CHUNK_EXPANSION,
            SearchStrategy.HIERARCHICAL,
            SearchStrategy.HYBRID
        ]
        
        print("🔄 Comparing search strategies...")
        print()
        
        for strategy in strategies:
            try:
                result = search_with_chunk_indices(
                    chunk_indices=chunk_indices,
                    strategy=strategy,
                    max_results=10
                )
                
                print(f"📊 {strategy.value.upper()}:")
                print(f"  Entities: {len(result.graph_context.entities)}")
                print(f"  Relationships: {len(result.graph_context.relationships)}")
                print(f"  Processing time: {result.processing_time_ms:.2f}ms")
                print(f"  Coverage: {result.coverage_score:.2f}")
                print(f"  Coherence: {result.coherence_score:.2f}")
                print(f"  Relevance: {result.relevance_score:.2f}")
                print()
                
            except Exception as e:
                print(f"❌ {strategy.value} failed: {e}")
                print()
        
    except Exception as e:
        print(f"❌ Error: {e}")


def main():
    """
    Run all examples.
    """
    print("🚀 Enterprise KG Hybrid Search Engine Examples")
    print("=" * 60)
    print()
    print("This demonstrates how to use chunk indices from vector similarity")
    print("search (e.g., Pinecone) with graph-based retrieval for enhanced results.")
    print()
    
    # Run examples
    example_basic_hybrid_search()
    example_filtered_search()
    example_entity_neighborhood_search()
    example_search_validation()
    example_strategy_comparison()
    
    print("\n✨ Examples completed!")
    print("\n💡 Integration Tips:")
    print("1. Replace chunk_indices with real results from your vector database")
    print("2. Adjust Neo4j connection parameters for your setup")
    print("3. Use entity/relationship type filters to focus results")
    print("4. Experiment with different strategies for different use cases")
    print("5. Monitor processing times and adjust expansion_depth accordingly")


if __name__ == "__main__":
    main()
