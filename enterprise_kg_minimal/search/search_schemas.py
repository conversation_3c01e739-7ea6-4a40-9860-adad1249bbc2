"""
Search-specific schema definitions for Enterprise KG Hybrid Search

This module defines data structures used by the hybrid search engine.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
from enum import Enum


class SearchStrategy(Enum):
    """Available search strategies."""
    ENTITY_CENTRIC = "entity_centric"
    RELATIONSHIP_CENTRIC = "relationship_centric"
    CHUNK_EXPANSION = "chunk_expansion"
    HIERARCHICAL = "hierarchical"
    HYBRID = "hybrid"
    SEMANTIC_ONLY = "semantic_only"  # New: Skip graph traversal entirely


class QueryComplexity(Enum):
    """
    Query complexity levels for routing decisions.
    """
    SIMPLE = "simple"           # Simple factual queries
    MODERATE = "moderate"       # Queries that might benefit from light graph context
    COMPLEX = "complex"         # Complex relational queries requiring full graph traversal


class RoutingDecision(Enum):
    """
    Routing decisions for query processing.
    """
    SEMANTIC_ONLY = "semantic_only"        # Use only vector search results
    LIGHT_GRAPH = "light_graph"           # Minimal graph expansion
    FULL_GRAPH = "full_graph"             # Full graph traversal
    ADAPTIVE = "adaptive"                 # Let system decide based on intermediate results


@dataclass
class SearchQuery:
    """
    Represents a search query with chunk indices and optional filters.
    """
    # Primary input - chunk indices from vector search
    chunk_indices: List[str]
    
    # Optional query text for context
    query_text: Optional[str] = None
    
    # Search configuration
    strategy: SearchStrategy = SearchStrategy.HYBRID
    max_results: int = 50
    expansion_depth: int = 2
    
    # Entity and relationship filters (from constants)
    entity_types: Optional[Set[str]] = None
    relationship_types: Optional[Set[str]] = None
    entity_categories: Optional[Set[str]] = None
    
    # Graph traversal parameters
    min_confidence_score: float = 0.3
    include_chunk_context: bool = True
    include_file_context: bool = True

    # Hybrid optimization parameters
    use_hybrid_traversal: bool = True
    priority_queue_limit: int = 1000
    connectivity_weight: float = 0.3
    cooccurrence_weight: float = 0.2
    confidence_weight: float = 0.4
    priority_inheritance_weight: float = 0.1

    # Relevance Scoring Weights
    # Used in _extract_entities_from_chunks
    score_weight_initial_entity_query_relevance_boost: float = 0.3

    # Used in _find_neighboring_entities (traditional)
    score_weight_neighbor_depth_penalty: float = 0.1
    score_weight_neighbor_confidence_boost: float = 0.2

    # Used in _find_neighboring_entities_hybrid
    # Existing: connectivity_weight, cooccurrence_weight, confidence_weight, priority_inheritance_weight
    score_weight_hybrid_query_relevance: float = 0.15
    score_weight_hybrid_depth_penalty: float = 0.05

    # Used in _calculate_query_entity_relevance (used by _extract_entities_from_chunks and _find_neighboring_entities_hybrid)
    score_weight_query_relevance_overlap: float = 0.4
    score_weight_query_relevance_semantic: float = 0.4
    score_weight_query_relevance_type: float = 0.2

    # Used in _calculate_keyword_overlap (which is used by _calculate_query_entity_relevance)
    score_weight_keyword_overlap_jaccard: float = 0.6
    score_weight_keyword_overlap_coverage: float = 0.4
 
    # Result ranking parameters
    boost_recent_entities: bool = True
    boost_high_importance: bool = True

    # Query routing parameters
    enable_intelligent_routing: bool = True
    routing_decision: Optional[RoutingDecision] = None  # Override automatic routing
    query_complexity: Optional[QueryComplexity] = None  # Override complexity detection
    vector_metadata: Optional[Dict[str, Any]] = None    # Pinecone metadata for analysis


@dataclass
class EntityMatch:
    """
    Represents an entity found during search.
    """
    name: str
    entity_type: str
    node_id: str
    
    # Properties from constants
    properties: Dict[str, Any] = field(default_factory=dict)
    
    # Search relevance
    relevance_score: float = 0.0
    match_reason: str = ""
    
    # Graph context
    chunk_sources: List[str] = field(default_factory=list)
    relationship_count: int = 0
    
    # Metadata
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class RelationshipMatch:
    """
    Represents a relationship found during search.
    """
    source_entity: str
    target_entity: str
    relationship_type: str
    
    # Properties and context
    properties: Dict[str, Any] = field(default_factory=dict)
    confidence_score: float = 0.0
    context: Optional[str] = None
    source_sentence: Optional[str] = None
    
    # Search relevance
    relevance_score: float = 0.0
    match_reason: str = ""
    
    # Graph context
    chunk_sources: List[str] = field(default_factory=list)
    
    # Metadata
    created_at: Optional[datetime] = None


@dataclass
class GraphContext:
    """
    Represents the graph context around search results.
    """
    # Core entities and relationships
    entities: List[EntityMatch] = field(default_factory=list)
    relationships: List[RelationshipMatch] = field(default_factory=list)
    
    # Chunk information
    source_chunks: List[Dict[str, Any]] = field(default_factory=list)
    chunk_count: int = 0
    
    # Graph statistics
    total_entities: int = 0
    total_relationships: int = 0
    entity_types_found: Set[str] = field(default_factory=set)
    relationship_types_found: Set[str] = field(default_factory=set)
    
    # Traversal information
    max_depth_reached: int = 0
    expansion_paths: List[List[str]] = field(default_factory=list)


@dataclass
class SearchResult:
    """
    Complete search result combining vector and graph information.
    """
    # Query information
    query: SearchQuery
    
    # Primary results
    graph_context: GraphContext
    
    # Result metadata
    total_results: int = 0
    processing_time_ms: float = 0.0
    strategy_used: SearchStrategy = SearchStrategy.HYBRID
    
    # Quality metrics
    coverage_score: float = 0.0  # How well the graph covers the query
    coherence_score: float = 0.0  # How connected the results are
    relevance_score: float = 0.0  # Overall relevance to query
    
    # Debug information
    debug_info: Dict[str, Any] = field(default_factory=dict)
    
    def get_top_entities(self, limit: int = 10) -> List[EntityMatch]:
        """Get top entities by relevance score."""
        return sorted(
            self.graph_context.entities,
            key=lambda e: e.relevance_score,
            reverse=True
        )[:limit]
    
    def get_top_relationships(self, limit: int = 10) -> List[RelationshipMatch]:
        """Get top relationships by relevance score."""
        return sorted(
            self.graph_context.relationships,
            key=lambda r: r.relevance_score,
            reverse=True
        )[:limit]
    
    def get_entity_by_type(self, entity_type: str) -> List[EntityMatch]:
        """Get all entities of a specific type."""
        return [e for e in self.graph_context.entities if e.entity_type == entity_type]
    
    def get_relationships_by_type(self, relationship_type: str) -> List[RelationshipMatch]:
        """Get all relationships of a specific type."""
        return [r for r in self.graph_context.relationships if r.relationship_type == relationship_type]


@dataclass
class QueryAnalysis:
    """
    Analysis results for query routing decisions.
    """
    # Query characteristics
    query_complexity: QueryComplexity
    complexity_score: float  # 0.0 to 1.0

    # Query patterns detected
    is_factual_query: bool = False
    is_relational_query: bool = False
    is_hierarchical_query: bool = False
    has_entity_references: bool = False
    has_relationship_keywords: bool = False

    # Metadata analysis
    metadata_sufficiency_score: float = 0.0  # 0.0 to 1.0
    metadata_contains_answer: bool = False

    # Routing recommendation
    recommended_decision: RoutingDecision
    confidence_score: float = 0.0  # Confidence in routing decision

    # Analysis details
    detected_entities: List[str] = field(default_factory=list)
    detected_relationships: List[str] = field(default_factory=list)
    query_keywords: List[str] = field(default_factory=list)
    reasoning: str = ""


@dataclass
class VectorMetadata:
    """
    Represents metadata from vector search results (e.g., Pinecone).
    """
    # Chunk information
    chunk_id: str
    file_id: str
    chunk_text: str

    # Similarity scores
    similarity_score: float

    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)

    # Content analysis
    content_length: int = 0
    entity_mentions: List[str] = field(default_factory=list)
    topic_tags: List[str] = field(default_factory=list)


@dataclass
class RoutingResult:
    """
    Result of query routing analysis.
    """
    # Routing decision
    decision: RoutingDecision
    confidence: float

    # Analysis used for decision
    query_analysis: QueryAnalysis

    # Execution parameters
    should_skip_graph: bool = False
    max_expansion_depth: int = 2
    suggested_strategy: SearchStrategy = SearchStrategy.HYBRID

    # Performance estimates
    estimated_time_ms: float = 0.0
    estimated_cost: float = 0.0

    # Reasoning
    reasoning: str = ""
    debug_info: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchMetrics:
    """
    Metrics for evaluating search performance.
    """
    # Timing metrics
    vector_search_time_ms: float = 0.0
    graph_expansion_time_ms: float = 0.0
    result_aggregation_time_ms: float = 0.0
    total_time_ms: float = 0.0
    
    # Result metrics
    chunks_processed: int = 0
    entities_found: int = 0
    relationships_found: int = 0
    expansion_iterations: int = 0
    
    # Quality metrics
    avg_entity_confidence: float = 0.0
    avg_relationship_confidence: float = 0.0
    graph_connectivity: float = 0.0  # How connected the result graph is
    
    # Coverage metrics
    entity_type_coverage: float = 0.0  # Percentage of available entity types found
    relationship_type_coverage: float = 0.0  # Percentage of available relationship types found
