#!/usr/bin/env python3
"""
Complete Example: Document Processing + Hybrid Search

This example demonstrates the complete workflow:
1. Process a document and store it in the knowledge graph
2. Simulate vector similarity search (getting chunk indices)
3. Use hybrid search to enrich results with graph context

This simulates the real-world scenario where:
- Documents are processed and stored in Neo4j
- Vector embeddings are stored in Pinecone
- User queries trigger vector similarity search in Pinecone
- Chunk indices from Pinecone are enriched with graph context
"""

import logging
from typing import List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def complete_workflow_example():
    """
    Demonstrate the complete workflow from document processing to hybrid search.
    """
    print("🚀 Complete Enterprise KG Workflow Example")
    print("=" * 60)
    print()
    
    # Sample enterprise document
    sample_document = """
    <PERSON> is the Senior Project Manager for the AI Initiative at TechCorp. 
    He reports directly to <PERSON>, who is the VP of Engineering.
    The AI Initiative is a strategic project focused on developing machine learning 
    capabilities for customer service automation.
    
    The project team includes several key members:
    - Dr. <PERSON> leads the Data Science team
    - <PERSON> is the Lead Software Engineer
    - <PERSON> manages the Quality Assurance process
    - <PERSON> handles the DevOps infrastructure
    
    The project uses several technologies including Python, TensorFlow, 
    AWS cloud services, and PostgreSQL database. The team collaborates 
    closely with the Product Management team led by Alex <PERSON>.
    
    Key milestones for Q2 include:
    - Complete the customer intent classification model
    - Deploy the initial chatbot prototype
    - Conduct user acceptance testing
    - Prepare for Q3 production rollout
    
    The initiative has a budget of $2.5M and is expected to reduce 
    customer service costs by 30% while improving response times.
    """
    
    # Step 1: Process the document
    print("📄 Step 1: Processing Document")
    print("-" * 30)
    
    try:
        # Import the main processing function
        from enterprise_kg_minimal import process_document
        
        # Process the document
        result = process_document(
            file_id="ai_initiative_doc_001",
            file_content=sample_document,
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="password",
            llm_provider="openai",  # or "anthropic"
            llm_model="gpt-4o"
        )
        
        if result["success"]:
            print(f"✅ Document processed successfully!")
            print(f"   File ID: {result['file_id']}")
            print(f"   Chunks created: {result['chunks_created']}")
            print(f"   Successful chunks: {result['successful_chunks']}")
            print(f"   Total entities: {result['total_entities']}")
            print(f"   Total relationships: {result['total_relationships']}")
            
            # Get chunk IDs for the next step
            chunk_ids = result.get('chunk_ids', [])
            print(f"   Chunk IDs: {chunk_ids}")
            
        else:
            print(f"❌ Document processing failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Error in document processing: {e}")
        print("   Make sure you have:")
        print("   - Neo4j running on bolt://localhost:7687")
        print("   - Valid OpenAI API key in environment")
        print("   - All required dependencies installed")
        return False
    
    # Step 2: Simulate vector similarity search
    print(f"\n🔍 Step 2: Simulating Vector Similarity Search")
    print("-" * 50)
    
    # In a real scenario, this would be:
    # 1. User submits query: "Who manages the AI project and what technologies are used?"
    # 2. Query is embedded using the same model used for document chunks
    # 3. Vector similarity search in Pinecone returns top-k chunk indices
    # 4. These chunk indices are passed to the hybrid search engine
    
    user_query = "Who manages the AI project and what technologies are used?"
    print(f"User query: {user_query}")
    print()
    print("🔄 In a real system, this would:")
    print("   1. Embed the query using the same model as document chunks")
    print("   2. Perform similarity search in Pinecone vector database")
    print("   3. Return top-k most similar chunk indices")
    print()
    
    # For this example, we'll use the chunk IDs from the document we just processed
    # In reality, these would come from Pinecone similarity search
    if 'chunk_ids' in locals() and chunk_ids:
        similar_chunk_indices = chunk_ids[:3]  # Take first 3 chunks as "similar"
        print(f"📊 Simulated Pinecone results (top similar chunks): {similar_chunk_indices}")
    else:
        # Fallback to mock chunk indices
        similar_chunk_indices = [
            "ai_initiative_doc_001_chunk_0_abc123",
            "ai_initiative_doc_001_chunk_1_def456"
        ]
        print(f"📊 Using mock chunk indices: {similar_chunk_indices}")
    
    # Step 3: Hybrid search with graph enrichment
    print(f"\n🧠 Step 3: Hybrid Search with Graph Enrichment")
    print("-" * 50)
    
    try:
        # Import the search function
        from enterprise_kg_minimal import search_knowledge_graph
        
        # Execute hybrid search
        search_result = search_knowledge_graph(
            chunk_indices=similar_chunk_indices,
            query_text=user_query,
            strategy="hybrid",
            max_results=20,
            expansion_depth=2,
            entity_types={"Person", "Manager", "Project", "Technology", "Company"},
            relationship_types={"manages", "works_for", "leads", "uses", "involved_in"}
        )
        
        if search_result["success"]:
            print(f"✅ Hybrid search completed successfully!")
            print(f"   Total results: {search_result['total_results']}")
            print(f"   Processing time: {search_result['processing_time_ms']:.2f}ms")
            print(f"   Strategy used: {search_result['strategy_used']}")
            print()
            
            # Display quality scores
            quality = search_result['quality_scores']
            print("📊 Quality Scores:")
            print(f"   Coverage: {quality['coverage']:.2f}")
            print(f"   Coherence: {quality['coherence']:.2f}")
            print(f"   Relevance: {quality['relevance']:.2f}")
            print()
            
            # Display top entities
            entities = search_result['entities']
            if entities:
                print("🏢 Top Entities Found:")
                for i, entity in enumerate(entities[:5], 1):
                    print(f"   {i}. {entity['name']} ({entity['type']})")
                    print(f"      Relevance: {entity['relevance_score']:.2f}")
                    print(f"      Chunk sources: {len(entity['chunk_sources'])}")
                print()
            
            # Display top relationships
            relationships = search_result['relationships']
            if relationships:
                print("🔗 Top Relationships Found:")
                for i, rel in enumerate(relationships[:5], 1):
                    print(f"   {i}. {rel['source']} --[{rel['type']}]--> {rel['target']}")
                    print(f"      Confidence: {rel['confidence_score']:.2f}")
                    print(f"      Relevance: {rel['relevance_score']:.2f}")
                    if rel['context']:
                        print(f"      Context: {rel['context'][:80]}...")
                print()
            
            # Display graph statistics
            stats = search_result['graph_statistics']
            print("📈 Graph Statistics:")
            print(f"   Entity types found: {len(stats['entity_types_found'])}")
            print(f"   Relationship types found: {len(stats['relationship_types_found'])}")
            print(f"   Max expansion depth: {stats['max_depth_reached']}")
            print(f"   Source chunks processed: {stats['source_chunks']}")
            print()
            
            # Show how this answers the user's query
            print("💡 Query Answer Summary:")
            print(f"   Query: {user_query}")
            print("   Answer based on graph enrichment:")
            
            # Find managers
            managers = [e for e in entities if e['type'] in ['Manager', 'Person'] and 'manager' in e['name'].lower()]
            if managers:
                print(f"   - Project managers: {', '.join(m['name'] for m in managers[:3])}")
            
            # Find technologies
            technologies = [e for e in entities if e['type'] in ['Technology', 'Tool', 'Platform']]
            if technologies:
                print(f"   - Technologies used: {', '.join(t['name'] for t in technologies[:5])}")
            
            # Find management relationships
            mgmt_rels = [r for r in relationships if r['type'] in ['manages', 'leads', 'reports_to']]
            if mgmt_rels:
                print("   - Management structure:")
                for rel in mgmt_rels[:3]:
                    print(f"     • {rel['source']} {rel['type']} {rel['target']}")
            
        else:
            print(f"❌ Hybrid search failed: {search_result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Error in hybrid search: {e}")
        return False
    
    # Step 4: Integration insights
    print(f"\n🔧 Step 4: Integration Insights")
    print("-" * 40)
    
    print("✨ This example demonstrates how to:")
    print("   1. Process enterprise documents into chunk-based knowledge graphs")
    print("   2. Integrate with vector databases (Pinecone) for similarity search")
    print("   3. Enrich vector search results with graph context")
    print("   4. Provide comprehensive answers using both vector and graph data")
    print()
    
    print("🏗️  Real-world integration would involve:")
    print("   • Storing document embeddings in Pinecone during processing")
    print("   • Using the same embedding model for queries and documents")
    print("   • Implementing caching for frequently accessed graph patterns")
    print("   • Adding real-time graph updates as new documents are processed")
    print("   • Scaling with multiple Neo4j instances and Pinecone indexes")
    print()
    
    print("📚 Next steps:")
    print("   • Experiment with different search strategies")
    print("   • Adjust entity and relationship type filters")
    print("   • Tune expansion depth based on your use case")
    print("   • Monitor performance and optimize for your data size")
    
    return True


def integration_tips():
    """
    Provide tips for integrating with real vector databases.
    """
    print("\n💡 Integration Tips for Production")
    print("=" * 40)
    
    print("\n🔗 Pinecone Integration:")
    print("""
    import pinecone
    from enterprise_kg_minimal import search_knowledge_graph
    
    # Initialize Pinecone
    pinecone.init(api_key="your-key", environment="your-env")
    index = pinecone.Index("your-index")
    
    # User query
    query = "Who manages the AI project?"
    query_embedding = get_embedding(query)  # Your embedding function
    
    # Vector similarity search
    vector_results = index.query(
        vector=query_embedding,
        top_k=10,
        include_metadata=True
    )
    
    # Extract chunk indices
    chunk_indices = [match['id'] for match in vector_results['matches']]
    
    # Enrich with graph context
    enriched_results = search_knowledge_graph(
        chunk_indices=chunk_indices,
        query_text=query,
        strategy="hybrid"
    )
    """)
    
    print("\n🚀 Performance Optimization:")
    print("   • Use connection pooling for Neo4j")
    print("   • Cache frequently accessed entity neighborhoods")
    print("   • Limit expansion depth for large graphs")
    print("   • Use entity/relationship type filters to reduce search space")
    print("   • Monitor query performance and adjust parameters")
    
    print("\n🔄 Real-time Updates:")
    print("   • Process new documents incrementally")
    print("   • Update both Neo4j graph and Pinecone vectors")
    print("   • Use graph versioning for consistency")
    print("   • Implement change detection for modified documents")


if __name__ == "__main__":
    success = complete_workflow_example()
    
    if success:
        integration_tips()
        print("\n🎉 Complete workflow example finished successfully!")
    else:
        print("\n❌ Workflow example encountered errors.")
        print("   Please check your setup and try again.")
    
    print("\n📖 For more examples, see:")
    print("   • enterprise_kg_minimal/search/example_usage.py")
    print("   • enterprise_kg_minimal/search/test_hybrid_search.py")
    print("   • enterprise_kg_minimal/search/README.md")
