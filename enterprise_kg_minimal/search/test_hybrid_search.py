#!/usr/bin/env python3
"""
Test script for Enterprise KG Hybrid Search Engine

This script provides basic tests and demonstrations of the hybrid search functionality.
It can be used to verify that the search engine is working correctly with your Neo4j setup.
"""

import logging
import sys
from typing import List, Set

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_search_engine_initialization():
    """Test that the search engine can be initialized properly."""
    print("🔧 Testing Search Engine Initialization")
    print("-" * 40)
    
    try:
        from .hybrid_search_engine import create_hybrid_search_engine
        
        # Create search engine with default settings
        search_engine = create_hybrid_search_engine(
            neo4j_uri="bolt://localhost:7687",
            neo4j_user="neo4j",
            neo4j_password="password"
        )
        
        # Test getting available types
        available_types = search_engine.get_available_types()
        
        print(f"✅ Search engine initialized successfully")
        print(f"   Entity types available: {available_types['total_entity_types']}")
        print(f"   Relationship types available: {available_types['total_relationship_types']}")
        print(f"   Entity categories: {len(available_types['entity_categories'])}")
        print(f"   Relationship categories: {len(available_types['relationship_categories'])}")
        
        # Clean up
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False


def test_parameter_validation():
    """Test parameter validation functionality."""
    print("\n✅ Testing Parameter Validation")
    print("-" * 40)
    
    try:
        from .hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine()
        
        # Test with valid parameters
        validation = search_engine.validate_search_parameters(
            chunk_indices=["chunk1", "chunk2"],
            entity_types={"Person", "Company"},
            relationship_types={"manages", "works_for"},
            expansion_depth=2,
            max_results=50
        )
        
        print(f"Valid parameters test: {'✅ PASS' if validation['valid'] else '❌ FAIL'}")
        
        # Test with invalid parameters
        validation = search_engine.validate_search_parameters(
            chunk_indices=[],  # Empty chunk indices
            entity_types={"InvalidType"},
            relationship_types={"invalid_relationship"},
            expansion_depth=0,  # Invalid depth
            max_results=0  # Invalid max results
        )
        
        print(f"Invalid parameters test: {'✅ PASS' if not validation['valid'] else '❌ FAIL'}")
        print(f"Errors detected: {len(validation['errors'])}")
        print(f"Warnings detected: {len(validation['warnings'])}")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Parameter validation test failed: {e}")
        return False


def test_mock_search():
    """Test search with mock chunk indices (may not return results if no data)."""
    print("\n🔍 Testing Mock Search")
    print("-" * 40)
    
    try:
        from .hybrid_search_engine import search_with_chunk_indices
        from .search_schemas import SearchStrategy
        
        # Mock chunk indices (these likely won't exist in the database)
        mock_chunk_indices = [
            "test_chunk_1",
            "test_chunk_2",
            "test_chunk_3"
        ]
        
        print(f"Testing with mock chunk indices: {mock_chunk_indices}")
        
        # Test basic search
        result = search_with_chunk_indices(
            chunk_indices=mock_chunk_indices,
            query_text="test query",
            strategy=SearchStrategy.HYBRID,
            max_results=10,
            expansion_depth=1
        )
        
        print(f"✅ Search completed successfully")
        print(f"   Total results: {result.total_results}")
        print(f"   Processing time: {result.processing_time_ms:.2f}ms")
        print(f"   Strategy used: {result.strategy_used.value}")
        print(f"   Entities found: {len(result.graph_context.entities)}")
        print(f"   Relationships found: {len(result.graph_context.relationships)}")
        
        if result.total_results == 0:
            print("   ℹ️  No results found (expected with mock data)")
        
        return True
        
    except Exception as e:
        print(f"❌ Mock search test failed: {e}")
        return False


def test_all_strategies():
    """Test all search strategies with mock data."""
    print("\n⚖️  Testing All Search Strategies")
    print("-" * 40)
    
    try:
        from .hybrid_search_engine import search_with_chunk_indices
        from .search_schemas import SearchStrategy
        
        mock_chunk_indices = ["test_chunk_1", "test_chunk_2"]
        
        strategies = [
            SearchStrategy.ENTITY_CENTRIC,
            SearchStrategy.RELATIONSHIP_CENTRIC,
            SearchStrategy.CHUNK_EXPANSION,
            SearchStrategy.HIERARCHICAL,
            SearchStrategy.HYBRID
        ]
        
        all_passed = True
        
        for strategy in strategies:
            try:
                result = search_with_chunk_indices(
                    chunk_indices=mock_chunk_indices,
                    strategy=strategy,
                    max_results=5,
                    expansion_depth=1
                )
                
                print(f"   {strategy.value}: ✅ PASS ({result.processing_time_ms:.1f}ms)")
                
            except Exception as e:
                print(f"   {strategy.value}: ❌ FAIL ({e})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Strategy testing failed: {e}")
        return False


def test_entity_neighborhood():
    """Test entity neighborhood search."""
    print("\n🌐 Testing Entity Neighborhood Search")
    print("-" * 40)
    
    try:
        from .hybrid_search_engine import create_hybrid_search_engine
        
        search_engine = create_hybrid_search_engine()
        
        # Test with a common entity name (may not exist)
        test_entity = "John Smith"
        
        result = search_engine.search_entity_neighborhood(
            entity_name=test_entity,
            max_depth=1,
            max_results=10
        )
        
        print(f"✅ Entity neighborhood search completed")
        print(f"   Entity: {test_entity}")
        print(f"   Connected entities: {len(result.graph_context.entities)}")
        print(f"   Relationships: {len(result.graph_context.relationships)}")
        print(f"   Processing time: {result.processing_time_ms:.2f}ms")
        
        if result.total_results == 0:
            print(f"   ℹ️  No neighborhood found for '{test_entity}' (entity may not exist)")
        
        search_engine.neo4j_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Entity neighborhood test failed: {e}")
        return False


def test_type_filtering():
    """Test search with type filtering."""
    print("\n🎯 Testing Type Filtering")
    print("-" * 40)
    
    try:
        from .hybrid_search_engine import search_with_chunk_indices
        from .search_schemas import SearchStrategy
        
        # Test with specific type filters
        entity_types = {"Person", "Company", "Project"}
        relationship_types = {"manages", "works_for", "involved_in"}
        
        result = search_with_chunk_indices(
            chunk_indices=["test_chunk_1"],
            entity_types=entity_types,
            relationship_types=relationship_types,
            strategy=SearchStrategy.ENTITY_CENTRIC,
            max_results=10
        )
        
        print(f"✅ Type filtering search completed")
        print(f"   Entity type filters: {entity_types}")
        print(f"   Relationship type filters: {relationship_types}")
        print(f"   Results: {result.total_results}")
        print(f"   Entity types found: {result.graph_context.entity_types_found}")
        print(f"   Relationship types found: {result.graph_context.relationship_types_found}")
        
        return True
        
    except Exception as e:
        print(f"❌ Type filtering test failed: {e}")
        return False


def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Enterprise KG Hybrid Search Engine Tests")
    print("=" * 60)
    print()
    
    tests = [
        ("Initialization", test_search_engine_initialization),
        ("Parameter Validation", test_parameter_validation),
        ("Mock Search", test_mock_search),
        ("All Strategies", test_all_strategies),
        ("Entity Neighborhood", test_entity_neighborhood),
        ("Type Filtering", test_type_filtering)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The hybrid search engine is working correctly.")
    else:
        print("⚠️  Some tests failed. Check your Neo4j connection and database setup.")
    
    print("\n💡 Notes:")
    print("- Tests use mock data, so empty results are expected")
    print("- Ensure Neo4j is running on bolt://localhost:7687")
    print("- Make sure the enterprise_kg database contains some graph data for meaningful results")
    print("- Adjust connection parameters in the test functions if needed")


if __name__ == "__main__":
    run_all_tests()
