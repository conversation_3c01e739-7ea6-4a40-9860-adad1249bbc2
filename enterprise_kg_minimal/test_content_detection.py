#!/usr/bin/env python3
"""
Content Type Detection Test

This script demonstrates how the enterprise_kg_minimal system automatically
detects content types and selects appropriate prompt generators.
"""

import os
import sys
from datetime import datetime

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.core.document_processor import detect_content_type, process_document
from enterprise_kg_minimal.core.prompt_generator import (
    create_full_prompt_generator, 
    create_feedback_focused_generator
)

def test_content_type_detection():
    """Test the content type detection function."""
    print("🔍 Testing Content Type Detection")
    print("=" * 50)
    
    test_cases = [
        {
            "name": "Customer Feedback Document",
            "file_id": "customer_feedback_001",
            "content": """
            Customer Feedback Report
            
            Customer: <PERSON>
            Product: CRM Software
            
            I'm very satisfied with this product. The customer service is excellent
            and the features work perfectly. I would definitely recommend this to others.
            The support team was very helpful when I had issues.
            """,
            "expected": "feedback"
        },
        {
            "name": "Project Document",
            "file_id": "project_alpha_status",
            "content": """
            Project Alpha Status Report
            
            Project Manager: <PERSON>line: Q1 2024
            
            The project is on track to meet all milestones. Key deliverables include
            system integration and stakeholder requirements gathering. The budget
            is within scope and all team members are aligned on objectives.
            """,
            "expected": "project"
        },
        {
            "name": "General Business Document",
            "file_id": "company_policy_doc",
            "content": """
            Company Policy Document
            
            This document outlines the standard operating procedures for
            employee onboarding. All new hires must complete orientation
            training and submit required documentation within 30 days.
            """,
            "expected": "general"
        },
        {
            "name": "Complaint Document",
            "file_id": "complaint_report_456",
            "content": """
            I am extremely disappointed with the service I received. The product
            is terrible and doesn't work as advertised. Customer support was
            unresponsive and unhelpful. This is completely unacceptable.
            """,
            "expected": "feedback"
        },
        {
            "name": "Review Document",
            "file_id": "product_review_789",
            "content": """
            Product Review: Mobile App
            
            Rating: 4/5 stars
            
            The app has good features but could use some improvements. The interface
            is user-friendly and the performance is generally good. However, there
            are occasional bugs that need to be fixed.
            """,
            "expected": "feedback"
        }
    ]
    
    print("Testing automatic content type detection:\n")
    
    for i, test_case in enumerate(test_cases, 1):
        detected_type = detect_content_type(test_case["content"], test_case["file_id"])
        
        status = "✅ PASS" if detected_type == test_case["expected"] else "❌ FAIL"
        
        print(f"{i}. {test_case['name']}")
        print(f"   File ID: {test_case['file_id']}")
        print(f"   Expected: {test_case['expected']}")
        print(f"   Detected: {detected_type}")
        print(f"   Status: {status}")
        print()
    
    return test_cases

def test_prompt_generator_selection():
    """Test that different prompt generators produce different prompts."""
    print("\n🎯 Testing Prompt Generator Selection")
    print("=" * 50)
    
    sample_feedback = """
    I love this product! The customer service is amazing and the features
    work exactly as expected. Highly recommended!
    """
    
    # Test general prompt generator
    general_generator = create_full_prompt_generator()
    general_prompt = general_generator.generate_relationship_extraction_prompt(sample_feedback)
    
    # Test feedback-focused prompt generator
    feedback_generator = create_feedback_focused_generator()
    feedback_prompt = feedback_generator.generate_feedback_sentiment_prompt(sample_feedback)
    
    print("📝 General Relationship Extraction Prompt (first 200 chars):")
    print("-" * 60)
    print(general_prompt[:200] + "...")
    print()
    
    print("😊 Feedback Sentiment Analysis Prompt (first 200 chars):")
    print("-" * 60)
    print(feedback_prompt[:200] + "...")
    print()
    
    # Check if they're different
    if general_prompt != feedback_prompt:
        print("✅ SUCCESS: Different prompt generators produce different prompts!")
    else:
        print("❌ ISSUE: Prompts are identical - this shouldn't happen")
    
    # Check for feedback-specific keywords in feedback prompt
    feedback_keywords = ['sentiment', 'feedback', 'customer', 'positive', 'negative']
    found_keywords = [kw for kw in feedback_keywords if kw.lower() in feedback_prompt.lower()]
    
    print(f"\n🔍 Feedback-specific keywords found in sentiment prompt: {found_keywords}")
    
    if len(found_keywords) >= 3:
        print("✅ SUCCESS: Feedback prompt contains appropriate sentiment analysis keywords!")
    else:
        print("❌ ISSUE: Feedback prompt may not be specialized enough")

def test_end_to_end_processing():
    """Test end-to-end processing with different content types."""
    print("\n🔄 Testing End-to-End Processing")
    print("=" * 50)
    
    test_documents = [
        {
            "name": "Feedback Document (Auto-detect)",
            "file_id": "feedback_auto_detect",
            "content": """
            Customer Feedback
            
            Customer: Test Customer
            Product: Demo Product
            
            I'm very happy with this purchase. The quality is excellent and
            the customer support team was very helpful. Highly recommended!
            """,
            "content_type": None  # Let system auto-detect
        },
        {
            "name": "Feedback Document (Manual Override)",
            "file_id": "general_document",
            "content": """
            General Business Document
            
            This document contains information about our company processes
            and standard operating procedures for various departments.
            """,
            "content_type": "feedback"  # Force feedback processing
        }
    ]
    
    for i, doc in enumerate(test_documents, 1):
        print(f"\n{i}. Testing: {doc['name']}")
        print(f"   File ID: {doc['file_id']}")
        print(f"   Content Type Override: {doc['content_type']}")
        
        try:
            # Note: This would require actual Neo4j and LLM connections
            # For demo purposes, we'll just show what would happen
            print(f"   📄 Content preview: {doc['content'][:100]}...")
            
            if doc['content_type'] is None:
                detected = detect_content_type(doc['content'], doc['file_id'])
                print(f"   🔍 Would auto-detect as: {detected}")
            else:
                print(f"   🎯 Would use manual override: {doc['content_type']}")
            
            print(f"   ⚙️ Would process with appropriate prompt generator")
            print(f"   ✅ Processing simulation complete")
            
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

def main():
    """Main test function."""
    print("🧪 Content Type Detection & Prompt Selection Test Suite")
    print("🏢 Enterprise KG Minimal System")
    print("=" * 70)
    
    print("\nThis test demonstrates:")
    print("• Automatic content type detection based on keywords")
    print("• Selection of appropriate prompt generators")
    print("• Specialized feedback sentiment analysis prompts")
    print("• Manual content type override capabilities")
    
    try:
        # Run tests
        test_content_type_detection()
        test_prompt_generator_selection()
        test_end_to_end_processing()
        
        print("\n🎉 All tests completed!")
        print("\n📚 Key Features Demonstrated:")
        print("1. ✅ Automatic content type detection from file_id and content")
        print("2. ✅ Different prompt generators for different content types")
        print("3. ✅ Specialized feedback sentiment analysis prompts")
        print("4. ✅ Manual content type override capability")
        print("5. ✅ Integration with document processing pipeline")
        
        print("\n🚀 Next Steps:")
        print("• Run the full feedback classification test suite")
        print("• Try processing your own feedback documents")
        print("• Experiment with manual content type overrides")
        print("• Customize entity types and relationships for your domain")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {str(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
