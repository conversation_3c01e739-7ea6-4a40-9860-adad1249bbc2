#!/usr/bin/env python3
"""
Example Usage for Enterprise KG Minimal

This script demonstrates how to use the clean modular package.
Run this from the parent directory (enterprise_kg) to test the module.

Usage:
    cd enterprise_kg
    python -c "
    from enterprise_kg_minimal import process_document
    
    result = process_document(
        file_id='example_doc',
        file_content='<PERSON> works at TechCorp on the AI project.',
        neo4j_uri='bolt://localhost:7687',
        neo4j_user='neo4j',
        neo4j_password='password'
    )
    
    print('Success:', result['success'])
    if result['success']:
        print('Chunks created:', result['chunks_created'])
        print('Total entities:', result['total_entities'])
        print('Total relationships:', result['total_relationships'])
    else:
        print('Error:', result['error'])
    "
"""

def example_usage():
    """Example of how to use the enterprise_kg_minimal package."""
    
    # Sample document content
    sample_content = """
    <PERSON> is the project manager for the AI Initiative at TechCorp. 
    He works closely with <PERSON>, who leads the engineering team.
    The project involves developing a new customer service platform using machine learning.
    The initiative is sponsored by the CTO, <PERSON>, and reports to the board quarterly.
    
    The team uses several technologies including Python, TensorFlow, and AWS cloud services.
    They collaborate with the data science team led by <PERSON>. <PERSON>.
    The project timeline spans 18 months with key milestones in Q2 and Q4.
    """
    
    print("📄 Sample Document Content:")
    print("-" * 50)
    print(sample_content.strip())
    print("-" * 50)
    
    print("\n🔧 To use this module, run the following from the parent directory:")
    print("cd enterprise_kg")
    print()
    print("python -c \"")
    print("from enterprise_kg_minimal import process_document")
    print()
    print("result = process_document(")
    print("    file_id='example_doc',")
    print("    file_content='''{}''',".format(sample_content.strip().replace("'", "\\'")))
    print("    neo4j_uri='bolt://localhost:7687',")
    print("    neo4j_user='neo4j',")
    print("    neo4j_password='your_password',")
    print("    llm_provider='openai',  # or 'anthropic'")
    print("    llm_model='gpt-4o'")
    print(")")
    print()
    print("print('Success:', result['success'])")
    print("if result['success']:")
    print("    print('Chunks created:', result['chunks_created'])")
    print("    print('Total entities:', result['total_entities'])")
    print("    print('Total relationships:', result['total_relationships'])")
    print("else:")
    print("    print('Error:', result['error'])")
    print("\"")
    
    print("\n📋 Expected Graph Structure:")
    print("File Node (example_doc)")
    print("├── CONTAINS → Chunk Node (example_doc_chunk_0_...)")
    print("│   ├── EXTRACTED_FROM → Entity: John Smith (Person)")
    print("│   ├── EXTRACTED_FROM → Entity: TechCorp (Company)")
    print("│   ├── EXTRACTED_FROM → Entity: AI Initiative (Project)")
    print("│   └── EXTRACTED_FROM → Entity: Sarah Johnson (Person)")
    print("├── CONTAINS → Chunk Node (example_doc_chunk_1_...)")
    print("│   ├── EXTRACTED_FROM → Entity: Michael Chen (Person)")
    print("│   └── EXTRACTED_FROM → Entity: Dr. Emily Rodriguez (Person)")
    print("└── Entity Relationships:")
    print("    ├── John Smith → MANAGES → AI Initiative")
    print("    ├── John Smith → WORKS_FOR → TechCorp")
    print("    ├── Sarah Johnson → LEADS → Engineering Team")
    print("    └── Michael Chen → SPONSORS → AI Initiative")
    
    print("\n🔍 Sample Neo4j Queries:")
    print("// Find all chunks for the document")
    print("MATCH (f:File {id: 'example_doc'})-[:CONTAINS]->(c:Chunk)")
    print("RETURN c")
    print()
    print("// Find all entities extracted from chunks")
    print("MATCH (c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)")
    print("WHERE c.id STARTS WITH 'example_doc'")
    print("RETURN e.name, e.entity_type")
    print()
    print("// Find relationships between entities")
    print("MATCH (e1:Entity)-[r]->(e2:Entity)")
    print("RETURN e1.name, type(r), e2.name")

if __name__ == "__main__":
    example_usage()
