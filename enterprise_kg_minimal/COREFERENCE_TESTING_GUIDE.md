# Coreference Resolution Testing Guide

## 🎯 Overview

This guide explains how to test the coreference resolution capabilities of the enterprise_kg_minimal system using real documents from the `documents/` folder.

## 📋 Prerequisites

### 1. Neo4j Database
- **Running Neo4j**: Ensure Neo4j is running on `bolt://localhost:7687`
- **Credentials**: Username `neo4j`, password `password`
- **Clean Database**: The test will automatically clear existing data

### 2. LLM API Access
- **OpenAI API Key**: Set environment variable `OPENAI_API_KEY`
- **Model**: Uses `gpt-4o` for entity extraction and coreference resolution

### 3. Python Dependencies
```bash
# Core dependencies (should already be installed)
pip install neo4j openai anthropic

# Optional: For PDF processing
pip install pdfplumber  # or PyPDF2
```

## 📄 Test Documents

The system includes sample documents designed to test coreference resolution:

### 1. `sample_business_document.txt`
**Coreference Challenges:**
- **Person variations**: "<PERSON>. <PERSON>" → "<PERSON>" → "<PERSON><PERSON> <PERSON>" → "She"
- **Organization variations**: "International Business Machines Corporation" → "IBM"
- **System references**: "Customer Relationship Management System" → "CRM" → "the system"
- **Cross-chunk references**: Same entities mentioned across different sections

### 2. `project_status_report.txt`
**Coreference Challenges:**
- **Professional titles**: "Dr. Amanda Rodriguez" → "Amanda" → "Dr. Rodriguez"
- **Role-based references**: "Robert Thompson" → "Robert" → "Thompson" → "the Project Manager"
- **Technical systems**: "Enterprise Resource Planning" → "ERP platform" → "the system"
- **Complex entity relationships**: Multiple people working on same projects

### 3. `Culture Values - Rapid.pdf` (if PDF libraries available)
**Real-world document** with natural coreference patterns

## 🚀 Running the Tests

### Quick Test (Recommended)
```bash
cd enterprise_kg
python enterprise_kg_minimal/run_kg_test.py
```

This interactive script will:
1. Check dependencies
2. List available documents
3. Ask for confirmation
4. Run the complete test
5. Show useful Neo4j queries

### Manual Test Execution
```bash
cd enterprise_kg
python enterprise_kg_minimal/test_complete_kg_formation.py
```

### Individual Component Tests
```bash
# Test coreference resolution specifically
python enterprise_kg_minimal/test_coreference_resolution.py

# Test content type detection
python enterprise_kg_minimal/test_content_detection.py
```

## 📊 What the Test Does

### 1. Database Preparation
- **Clears Neo4j**: Removes all existing nodes and relationships
- **Verifies clean state**: Confirms database is empty

### 2. Document Processing
For each document:
- **Text extraction**: Reads text files or extracts from PDFs
- **Content type detection**: Auto-detects document type
- **Chunking**: Splits document into manageable chunks
- **Entity extraction**: Extracts entities with coreference resolution
- **Graph creation**: Builds knowledge graph in Neo4j

### 3. Coreference Resolution
- **Cross-chunk linking**: Connects same entities across chunks
- **Pronoun resolution**: Replaces "he", "she", "it" with actual names
- **Name standardization**: Uses consistent canonical names
- **Alias tracking**: Maintains mapping of name variations

### 4. Analysis and Reporting
- **Graph statistics**: Counts nodes, relationships, entity types
- **Resolution metrics**: Shows coreference resolution effectiveness
- **Detailed report**: Generates comprehensive test report

## 🔍 Expected Results

### Before Coreference Resolution
```
Entities: Dr. Sarah Johnson, Sarah, Dr. Johnson, she, the CEO (5 separate entities)
Graph: 5 disconnected nodes for the same person
```

### After Coreference Resolution
```
Entities: Dr. Sarah Johnson (1 canonical entity)
Aliases: Sarah, Dr. Johnson, she, the CEO → Dr. Sarah Johnson
Graph: 1 node with proper relationships
```

### Typical Improvements
- **60-80% reduction** in duplicate entities
- **Better connectivity** between related entities
- **More accurate relationships** due to consistent naming
- **Cleaner graph structure** for analysis and querying

## 📈 Analyzing Results

### 1. Test Report
The test generates a detailed report: `kg_formation_test_report_YYYYMMDD_HHMMSS.md`

**Report includes:**
- Document processing statistics
- Entity and relationship counts
- Coreference resolution metrics
- Graph analysis results

### 2. Neo4j Exploration

**Access Neo4j Browser:**
- URL: http://localhost:7474
- Username: `neo4j`
- Password: `password`

**Key Queries to Run:**

```cypher
// 1. Overall graph statistics
MATCH (n)
OPTIONAL MATCH ()-[r]->()
RETURN count(DISTINCT n) as nodes, count(DISTINCT r) as relationships

// 2. Entity types and counts
MATCH (e:Entity)
RETURN e.entity_type, count(e) as count, collect(e.name)[0..3] as examples
ORDER BY count DESC

// 3. Find resolved entities (with aliases)
MATCH (e:Entity)
WHERE e.aliases IS NOT NULL
RETURN e.name, e.entity_type, e.aliases

// 4. Person entities and their relationships
MATCH (p:Entity {entity_type: 'Person'})-[r]-(other)
RETURN p.name, type(r), other.name, other.entity_type
LIMIT 20

// 5. Cross-chunk entity connections
MATCH (f:File)-[:CONTAINS]->(c1:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
MATCH (f)-[:CONTAINS]->(c2:Chunk)-[:EXTRACTED_FROM]->(e)
WHERE c1 <> c2
RETURN e.name, e.entity_type, count(DISTINCT c1) + count(DISTINCT c2) as chunk_count
ORDER BY chunk_count DESC
```

## 🎯 Validation Checklist

### ✅ Successful Coreference Resolution
- [ ] Same person appears as one entity (not multiple)
- [ ] Pronouns are resolved to actual names
- [ ] Organizations have consistent names (IBM vs International Business Machines)
- [ ] Systems are properly linked (CRM vs Customer Relationship Management System)
- [ ] Cross-chunk references are connected

### ✅ Graph Quality
- [ ] Reasonable entity count (not excessive duplicates)
- [ ] Meaningful relationships between entities
- [ ] Proper entity types assigned
- [ ] File → Chunk → Entity structure maintained

### ✅ Performance
- [ ] Processing completes without errors
- [ ] Reasonable processing time (< 5 minutes for sample docs)
- [ ] Memory usage stays reasonable
- [ ] Neo4j graph loads properly

## 🔧 Troubleshooting

### Common Issues

**1. Neo4j Connection Failed**
```bash
# Check if Neo4j is running
docker ps | grep neo4j
# Or check service status
systemctl status neo4j
```

**2. OpenAI API Key Missing**
```bash
export OPENAI_API_KEY="your-api-key-here"
```

**3. PDF Processing Failed**
```bash
pip install pdfplumber
# Or skip PDFs and test with .txt files only
```

**4. Low Entity Resolution**
- Check if coreference resolution is enabled
- Verify prompts include coreference instructions
- Review LLM responses for entity consistency

**5. Memory Issues**
- Reduce chunk size: `chunk_size=500`
- Process fewer documents at once
- Clear Neo4j database between tests

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Run test with detailed logging
python enterprise_kg_minimal/test_complete_kg_formation.py
```

## 📚 Understanding the Results

### Coreference Resolution Metrics
- **Total entities**: Raw count before resolution
- **Unique entities**: Count after resolution
- **Resolution rate**: Percentage of entities that were merged
- **Clusters**: Groups of coreferent entities

### Graph Structure
- **File nodes**: Represent source documents
- **Chunk nodes**: Document sections
- **Entity nodes**: Extracted entities with resolved names
- **Relationships**: Connections between entities

### Quality Indicators
- **High resolution rate** (>30%): Good coreference detection
- **Reasonable entity count**: Not too many duplicates
- **Connected components**: Entities properly linked
- **Meaningful relationships**: Business-relevant connections

## 🎉 Success Criteria

The test is successful if:
1. **Documents process without errors**
2. **Entities are properly resolved** (fewer duplicates)
3. **Graph structure is coherent** (File→Chunk→Entity)
4. **Relationships are meaningful** (business-relevant connections)
5. **Coreference resolution shows improvement** (>20% resolution rate)

## 🚀 Next Steps

After successful testing:
1. **Deploy with confidence**: Enable coreference resolution in production
2. **Customize for your domain**: Add domain-specific entity types
3. **Tune parameters**: Adjust resolution thresholds
4. **Scale up**: Process larger document collections
5. **Integrate with applications**: Use the resolved entities in your systems
