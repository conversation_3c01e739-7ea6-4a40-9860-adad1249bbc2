# Customer Feedback Classification Guide

## Overview

The enterprise_kg_minimal system now supports comprehensive customer feedback classification with sentiment analysis capabilities. This feature enables organizations to automatically process customer feedback, extract insights, and generate actionable recommendations.

## Features

### 🎯 Sentiment Analysis
- **Positive/Negative/Neutral/Mixed** sentiment classification
- **Sentiment scoring** from -1.0 (very negative) to 1.0 (very positive)
- **Confidence scoring** for classification reliability

### 📊 Entity Extraction
- **Customer entities**: Identify feedback providers
- **Product/Service entities**: What is being reviewed
- **Feedback entities**: Specific feedback content
- **Sentiment entities**: Emotional expressions
- **Complaint/Compliment entities**: Issues and praise

### 🔗 Relationship Mapping
- `provides_feedback`: Customer → Product/Service
- `expresses_sentiment`: Feedback → Sentiment
- `rates`: Customer → Product/Service
- `complains_about`: Customer → Issue
- `praises`: Customer → Feature
- `experiences`: Customer → Issue/Benefit

### 📈 Analysis Capabilities
- Sentiment distribution analysis
- Common issues identification
- Customer satisfaction trends
- Product performance insights
- Actionable recommendations

## Quick Start

### 1. Basic Feedback Processing

```python
from enterprise_kg_minimal import process_document

# Process a customer feedback document
feedback_content = """
Customer Feedback Report

Customer: <PERSON>
Product: CRM Software
Date: 2024-01-15

I'm very satisfied with the CRM software. The user interface is intuitive 
and our sales team productivity has increased significantly. The customer 
support team has been incredibly helpful. Highly recommended!
"""

result = process_document(
    file_id="feedback_001",
    file_content=feedback_content,
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    llm_provider="openai",
    llm_model="gpt-4o"
)

print(f"Entities extracted: {result['total_entities']}")
print(f"Relationships found: {result['total_relationships']}")
```

### 2. Using Sample Data

```python
from enterprise_kg_minimal.sample_data.customer_feedback_samples import (
    get_sample_feedback_data,
    generate_feedback_file_content
)

# Get sample feedback data
samples = get_sample_feedback_data()
positive_samples = get_positive_feedback_samples()
negative_samples = get_negative_feedback_samples()

# Process a sample
sample = samples[0]
file_content = generate_feedback_file_content(sample)
result = process_document(
    file_id=f"sample_{sample['feedback_id']}",
    file_content=file_content
)
```

### 3. Batch Analysis

```python
from enterprise_kg_minimal.utils.feedback_analyzer import (
    analyze_feedback_batch,
    generate_feedback_report
)

# Process multiple feedback samples
results = []
for sample in get_sample_feedback_data():
    file_content = generate_feedback_file_content(sample)
    result = process_document(
        file_id=f"batch_{sample['feedback_id']}",
        file_content=file_content
    )
    results.append(result)

# Analyze results
analysis = analyze_feedback_batch(results)
report = generate_feedback_report(analysis)

print(report)
```

## Sample Data

The system includes 10 diverse feedback samples:

### Positive Feedback (3 samples)
- **FB001**: CRM Software satisfaction
- **FB002**: Cloud hosting service praise  
- **FB003**: Mobile app appreciation

### Negative Feedback (3 samples)
- **FB004**: E-commerce platform issues
- **FB005**: Customer support complaints
- **FB006**: Data analytics tool problems

### Mixed/Neutral Feedback (4 samples)
- **FB007**: Project management software review
- **FB008**: Security software evaluation
- **FB009**: Enterprise software suite mixed experience
- **FB010**: API service balanced review

## Testing

### Run Comprehensive Tests

```bash
cd enterprise_kg
python enterprise_kg_minimal/test_feedback_classification.py
```

### Run Demo

```bash
python enterprise_kg_minimal/examples/feedback_classification_demo.py
```

## Neo4j Queries

### Find All Customer Feedback
```cypher
MATCH (f:File)-[:CONTAINS]->(c:Chunk)-[:EXTRACTED_FROM]->(e:Entity)
WHERE f.id STARTS WITH 'feedback_'
RETURN f.id, e.name, e.entity_type
```

### Find Sentiment Relationships
```cypher
MATCH (e1:Entity)-[r]->(e2:Entity)
WHERE type(r) IN ['expresses_sentiment', 'provides_feedback']
RETURN e1.name, type(r), e2.name
```

### Find Customer-Product Relationships
```cypher
MATCH (customer:Entity)-[r]->(product:Entity)
WHERE customer.entity_type = 'Customer' 
AND product.entity_type IN ['Product', 'Service']
RETURN customer.name, type(r), product.name
```

### Find Complaints and Issues
```cypher
MATCH (customer:Entity)-[r:complains_about]->(issue:Entity)
RETURN customer.name, issue.name, issue.entity_type
```

## Analysis Results

The feedback analyzer provides:

### Sentiment Distribution
- Overall sentiment percentages
- Average sentiment scores
- Positive/negative ratios

### Common Issues
- Top complaint keywords
- Product-specific issues
- Issue frequency analysis

### Customer Satisfaction
- Customer satisfaction averages
- Product performance rankings
- Satisfaction trends over time

### Recommendations
- High-priority action items
- Product improvement suggestions
- Customer service enhancements

## Customization

### Adding New Entity Types

```python
# In constants/entities.py
class EntityType(Enum):
    # Add new feedback-related types
    FEATURE_REQUEST = "Feature_Request"
    BUG_REPORT = "Bug_Report"
    USABILITY_ISSUE = "Usability_Issue"
```

### Adding New Relationships

```python
# In constants/relationships.py
class RelationshipType(Enum):
    # Add new feedback relationships
    REQUESTS_FEATURE = "requests_feature"
    REPORTS_BUG = "reports_bug"
    SUGGESTS_IMPROVEMENT = "suggests_improvement"
```

### Custom Prompt Generation

```python
from enterprise_kg_minimal.core.prompt_generator import create_feedback_focused_generator

# Create feedback-specific prompt generator
generator = create_feedback_focused_generator()

# Generate specialized sentiment analysis prompt
prompt = generator.generate_feedback_sentiment_prompt(feedback_content)
```

## Best Practices

### 1. Data Preparation
- Ensure feedback text is clean and well-formatted
- Include customer and product information when available
- Use consistent feedback document structure

### 2. Chunk Size Optimization
- Use smaller chunk sizes (600-800 tokens) for feedback
- Increase overlap (100-200 tokens) to preserve context
- Test different chunking strategies for your data

### 3. Entity Type Selection
- Focus on feedback-relevant entity types
- Use the feedback-focused prompt generator
- Customize entity types for your domain

### 4. Quality Assurance
- Validate sentiment classification accuracy
- Review extracted entities and relationships
- Monitor false positives and negatives

### 5. Continuous Improvement
- Regularly update entity and relationship types
- Refine prompts based on results
- Incorporate domain-specific terminology

## Integration Examples

### With Customer Support Systems
```python
def process_support_ticket(ticket_data):
    feedback_content = f"""
    Support Ticket: {ticket_data['ticket_id']}
    Customer: {ticket_data['customer_name']}
    Product: {ticket_data['product']}
    Priority: {ticket_data['priority']}
    
    Issue Description:
    {ticket_data['description']}
    """
    
    return process_document(
        file_id=f"ticket_{ticket_data['ticket_id']}",
        file_content=feedback_content
    )
```

### With Survey Data
```python
def process_survey_response(survey_data):
    feedback_content = f"""
    Survey Response: {survey_data['response_id']}
    Customer: {survey_data['customer_id']}
    Survey: {survey_data['survey_name']}
    
    Responses:
    {survey_data['responses']}
    """
    
    return process_document(
        file_id=f"survey_{survey_data['response_id']}",
        file_content=feedback_content
    )
```

## Troubleshooting

### Common Issues

1. **Low Entity Extraction**: Use feedback-focused prompt generator
2. **Incorrect Sentiment**: Verify LLM model and prompt quality
3. **Missing Relationships**: Check relationship type definitions
4. **Performance Issues**: Optimize chunk size and overlap

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed logging for debugging
result = process_document(
    file_id="debug_feedback",
    file_content=feedback_content,
    # ... other parameters
)
```

## Support

For questions and support:
- Review the main enterprise_kg_minimal documentation
- Check the test scripts for examples
- Examine the sample data for reference patterns
- Use the demo script to understand the workflow
