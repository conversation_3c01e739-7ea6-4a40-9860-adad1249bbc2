# Intelligent Query Routing for Enterprise KG

## Overview

The Enterprise KG Hybrid Search Engine now includes an **Intelligent Query Routing** layer that automatically determines whether graph traversal is necessary or if semantic search alone is sufficient to answer a query. This optimization can significantly improve performance by avoiding expensive graph operations when they don't add value.

## Key Features

### 1. **Query Complexity Analysis**
- Automatically analyzes query patterns to determine complexity
- Classifies queries as SIMPLE, MODERATE, or COMPLEX
- Detects factual, relational, and hierarchical query types

### 2. **Vector Metadata Analysis**
- Analyzes Pinecone metadata to assess information sufficiency
- Determines if vector search results contain direct answers
- Calculates content quality and completeness scores

### 3. **Intelligent Routing Decisions**
- **SEMANTIC_ONLY**: Skip graph traversal entirely
- **LIGHT_GRAPH**: Minimal graph expansion (depth 1)
- **FULL_GRAPH**: Complete graph traversal
- **ADAPTIVE**: Dynamic decision based on intermediate results

### 4. **Performance Optimization**
- Estimates time and cost savings
- Provides fallback mechanisms
- Supports manual overrides for specific use cases

## Architecture

```
Query + Chunk Indices + Vector Metadata
                ↓
        Query Analyzer
                ↓
    Vector Metadata Analyzer
                ↓
        Search Router
                ↓
    Routing Decision + Parameters
                ↓
    Hybrid Search Engine
                ↓
        Optimized Results
```

## Components

### QueryAnalyzer
Analyzes query text to determine:
- Query complexity and patterns
- Entity and relationship mentions
- Factual vs. relational nature
- Recommended routing strategy

### VectorMetadataAnalyzer
Analyzes vector search metadata to determine:
- Content sufficiency for answering queries
- Presence of direct answers
- Quality and completeness scores
- Need for additional graph context

### SearchRouter
Combines analyses to make routing decisions:
- Integrates query and metadata analysis
- Applies configurable thresholds
- Provides reasoning for decisions
- Estimates performance impact

## Usage Examples

### Basic Usage with Automatic Routing

```python
from enterprise_kg_minimal.search import create_hybrid_search_engine

# Create engine with intelligent routing (enabled by default)
search_engine = create_hybrid_search_engine(
    neo4j_uri="bolt://localhost:7687",
    neo4j_user="neo4j",
    neo4j_password="password",
    enable_intelligent_routing=True
)

# Simple query - will likely use semantic search only
result = search_engine.search(
    chunk_indices=["chunk_1", "chunk_2", "chunk_3"],
    query_text="What is the company leave policy?",
    vector_metadata=pinecone_metadata  # Optional
)

# Check routing decision
if result.debug_info.get('graph_traversal_skipped'):
    print("Used semantic search only!")
else:
    print("Used graph traversal for comprehensive results")
```

### Manual Override

```python
from enterprise_kg_minimal.search import RoutingDecision

# Force semantic-only search
result = search_engine.search(
    chunk_indices=chunk_indices,
    query_text=query_text,
    routing_decision=RoutingDecision.SEMANTIC_ONLY
)

# Force full graph traversal
result = search_engine.search(
    chunk_indices=chunk_indices,
    query_text=query_text,
    routing_decision=RoutingDecision.FULL_GRAPH
)
```

### Using Individual Components

```python
from enterprise_kg_minimal.search import (
    SearchRouter, 
    QueryAnalyzer, 
    VectorMetadataAnalyzer
)

# Analyze query complexity
analyzer = QueryAnalyzer()
analysis = analyzer.analyze_query("Who reports to the CEO?")
print(f"Complexity: {analysis.query_complexity}")
print(f"Recommendation: {analysis.recommended_decision}")

# Analyze metadata sufficiency
metadata_analyzer = VectorMetadataAnalyzer()
dummy_metadata = metadata_analyzer.create_dummy_metadata(
    chunk_indices, query_text
)
sufficiency = metadata_analyzer.analyze_metadata_sufficiency(
    query_text, dummy_metadata
)

# Make routing decision
router = SearchRouter()
routing_result = router.route_query(
    query_text=query_text,
    chunk_indices=chunk_indices,
    vector_metadata=dummy_metadata
)
```

## Query Types and Routing Behavior

### Simple Queries (→ SEMANTIC_ONLY)
- **Leave policy questions**: "What is the vacation policy?"
- **Contact information**: "HR department phone number"
- **Definitions**: "What does PTO mean?"
- **Procedures**: "How to submit expense reports"

### Moderate Queries (→ LIGHT_GRAPH)
- **Department relationships**: "How does IT relate to security?"
- **Process connections**: "What approvals are needed for hiring?"
- **Policy interactions**: "How do remote work and expense policies connect?"

### Complex Queries (→ FULL_GRAPH)
- **Hierarchical questions**: "Who reports to whom in the engineering team?"
- **Multi-hop relationships**: "How are projects connected to budgets and teams?"
- **Network analysis**: "What are all the dependencies for this process?"

## Configuration

### Routing Thresholds

```python
# Update routing thresholds
search_router.update_thresholds({
    'semantic_only_confidence': 0.8,
    'metadata_sufficiency': 0.7,
    'complexity_threshold': 0.4,
    'similarity_threshold': 0.6
})
```

### Disable Intelligent Routing

```python
# Create engine without intelligent routing
search_engine = create_hybrid_search_engine(
    enable_intelligent_routing=False
)
```

## Integration with Pinecone

The system is designed to work with Pinecone metadata:

```python
# Example Pinecone metadata structure
pinecone_metadata = [
    VectorMetadata(
        chunk_id="chunk_1",
        file_id="policy_doc_1",
        chunk_text="Leave policy: Employees get 15 days...",
        similarity_score=0.95,
        metadata={
            "file_name": "employee_handbook.pdf",
            "section": "Leave Policies",
            "department": "HR"
        }
    )
]

# Use with search
result = search_engine.search(
    chunk_indices=["chunk_1", "chunk_2"],
    query_text="What is the leave policy?",
    vector_metadata=pinecone_metadata
)
```

## Performance Benefits

### Time Savings
- **Semantic-only queries**: 50-200ms (vs 500-2000ms with graph)
- **Light graph queries**: 200-500ms (vs 500-2000ms with full graph)
- **Adaptive routing**: Optimal performance based on query needs

### Cost Savings
- Reduced Neo4j query load
- Lower computational overhead
- Faster response times for simple queries

### Quality Maintenance
- Full graph traversal when needed for complex queries
- Fallback mechanisms ensure comprehensive results
- Manual override capability for edge cases

## Monitoring and Debugging

### Debug Information
Every search result includes routing information:

```python
result = search_engine.search(...)

# Check routing decision
print(result.debug_info['routing_decision'])
print(result.debug_info['routing_reasoning'])
print(result.debug_info['graph_traversal_skipped'])
print(result.debug_info['estimated_time_saved_ms'])
```

### Routing Statistics
```python
# Get routing statistics (future feature)
stats = search_router.get_routing_stats()
print(stats['thresholds'])
print(stats['analyzer_status'])
```

## Best Practices

1. **Use with Vector Metadata**: Provide Pinecone metadata when available for better routing decisions
2. **Monitor Performance**: Track routing decisions and adjust thresholds as needed
3. **Test Query Types**: Validate routing behavior with your specific query patterns
4. **Manual Override**: Use manual overrides for critical queries that need guaranteed graph traversal
5. **Fallback Planning**: Ensure your application can handle both semantic-only and graph-enhanced results

## Future Enhancements

- **Machine Learning Models**: Replace rule-based routing with learned models
- **Query History Analysis**: Use past query patterns to improve routing decisions
- **Dynamic Threshold Adjustment**: Automatically tune thresholds based on performance metrics
- **A/B Testing Framework**: Compare routing strategies for optimization
- **Real-time Performance Monitoring**: Track and optimize routing decisions in production
