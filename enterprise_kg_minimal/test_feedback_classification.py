#!/usr/bin/env python3
"""
Customer Feedback Classification Test Script

This script tests the enterprise_kg_minimal system's ability to classify
customer feedback with sentiment analysis and entity extraction.

Usage:
    python test_feedback_classification.py
"""

import os
import sys
import json
import logging
from typing import Dict, List, Any
from datetime import datetime

# Add the parent directory to the path to import enterprise_kg_minimal
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from enterprise_kg_minimal.core.document_processor import process_document
from enterprise_kg_minimal.sample_data.customer_feedback_samples import (
    get_sample_feedback_data,
    get_positive_feedback_samples,
    get_negative_feedback_samples,
    get_mixed_neutral_feedback_samples,
    get_high_priority_feedback,
    generate_feedback_file_content
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FeedbackClassificationTester:
    """Test customer feedback classification capabilities."""
    
    def __init__(self, 
                 neo4j_uri: str = "bolt://localhost:7687",
                 neo4j_user: str = "neo4j",
                 neo4j_password: str = "password",
                 llm_provider: str = "openai",
                 llm_model: str = "gpt-4o"):
        """Initialize the tester with connection parameters."""
        self.neo4j_uri = neo4j_uri
        self.neo4j_user = neo4j_user
        self.neo4j_password = neo4j_password
        self.llm_provider = llm_provider
        self.llm_model = llm_model
        self.test_results = []
        
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run comprehensive feedback classification tests."""
        logger.info("🚀 Starting comprehensive customer feedback classification test")
        
        # Get sample data
        all_feedback = get_sample_feedback_data()
        positive_feedback = get_positive_feedback_samples()
        negative_feedback = get_negative_feedback_samples()
        mixed_neutral_feedback = get_mixed_neutral_feedback_samples()
        high_priority_feedback = get_high_priority_feedback()
        
        logger.info(f"📊 Test Dataset Summary:")
        logger.info(f"   Total feedback samples: {len(all_feedback)}")
        logger.info(f"   Positive samples: {len(positive_feedback)}")
        logger.info(f"   Negative samples: {len(negative_feedback)}")
        logger.info(f"   Mixed/Neutral samples: {len(mixed_neutral_feedback)}")
        logger.info(f"   High priority samples: {len(high_priority_feedback)}")
        
        # Test each feedback sample
        test_summary = {
            "total_tests": len(all_feedback),
            "successful_tests": 0,
            "failed_tests": 0,
            "sentiment_accuracy": 0.0,
            "entity_extraction_success": 0,
            "relationship_extraction_success": 0,
            "test_results": [],
            "test_timestamp": datetime.now().isoformat()
        }
        
        for i, feedback_data in enumerate(all_feedback):
            logger.info(f"🔍 Testing feedback {i+1}/{len(all_feedback)}: {feedback_data['feedback_id']}")
            
            try:
                # Generate file content
                file_content = generate_feedback_file_content(feedback_data)
                file_id = f"feedback_{feedback_data['feedback_id']}"
                
                # Process the feedback document
                result = process_document(
                    file_id=file_id,
                    file_content=file_content,
                    neo4j_uri=self.neo4j_uri,
                    neo4j_user=self.neo4j_user,
                    neo4j_password=self.neo4j_password,
                    llm_provider=self.llm_provider,
                    llm_model=self.llm_model,
                    chunking_strategy="hybrid",
                    chunk_size=800,  # Smaller chunks for feedback
                    chunk_overlap=100
                )
                
                # Analyze results
                test_result = self._analyze_test_result(feedback_data, result)
                test_summary["test_results"].append(test_result)
                
                if test_result["success"]:
                    test_summary["successful_tests"] += 1
                    if test_result["entities_found"] > 0:
                        test_summary["entity_extraction_success"] += 1
                    if test_result["relationships_found"] > 0:
                        test_summary["relationship_extraction_success"] += 1
                else:
                    test_summary["failed_tests"] += 1
                    
                logger.info(f"   ✅ Success: {test_result['success']}")
                logger.info(f"   📝 Entities: {test_result['entities_found']}")
                logger.info(f"   🔗 Relationships: {test_result['relationships_found']}")
                
            except Exception as e:
                logger.error(f"   ❌ Test failed: {str(e)}")
                test_summary["failed_tests"] += 1
                test_summary["test_results"].append({
                    "feedback_id": feedback_data["feedback_id"],
                    "success": False,
                    "error": str(e),
                    "entities_found": 0,
                    "relationships_found": 0
                })
        
        # Calculate final metrics
        if test_summary["successful_tests"] > 0:
            test_summary["entity_extraction_rate"] = (
                test_summary["entity_extraction_success"] / test_summary["successful_tests"]
            )
            test_summary["relationship_extraction_rate"] = (
                test_summary["relationship_extraction_success"] / test_summary["successful_tests"]
            )
        
        return test_summary
    
    def _analyze_test_result(self, feedback_data: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the test result for a single feedback sample."""
        return {
            "feedback_id": feedback_data["feedback_id"],
            "customer_name": feedback_data["customer_name"],
            "expected_sentiment": feedback_data["expected_sentiment"],
            "expected_score": feedback_data["expected_score"],
            "priority_level": feedback_data["priority_level"],
            "success": result.get("success", False),
            "chunks_created": result.get("chunks_created", 0),
            "entities_found": result.get("total_entities", 0),
            "relationships_found": result.get("total_relationships", 0),
            "processing_error": result.get("error"),
            "chunk_details": result.get("chunk_details", [])
        }
    
    def test_specific_feedback_types(self) -> Dict[str, Any]:
        """Test specific types of feedback separately."""
        logger.info("🎯 Testing specific feedback types")
        
        type_tests = {
            "positive": get_positive_feedback_samples(),
            "negative": get_negative_feedback_samples(),
            "mixed_neutral": get_mixed_neutral_feedback_samples(),
            "high_priority": get_high_priority_feedback()
        }
        
        results = {}
        
        for feedback_type, samples in type_tests.items():
            logger.info(f"📊 Testing {feedback_type} feedback ({len(samples)} samples)")
            
            type_results = {
                "total_samples": len(samples),
                "successful_extractions": 0,
                "total_entities": 0,
                "total_relationships": 0,
                "sample_results": []
            }
            
            for sample in samples[:3]:  # Test first 3 of each type
                try:
                    file_content = generate_feedback_file_content(sample)
                    file_id = f"test_{feedback_type}_{sample['feedback_id']}"
                    
                    result = process_document(
                        file_id=file_id,
                        file_content=file_content,
                        neo4j_uri=self.neo4j_uri,
                        neo4j_user=self.neo4j_user,
                        neo4j_password=self.neo4j_password,
                        llm_provider=self.llm_provider,
                        llm_model=self.llm_model
                    )
                    
                    if result.get("success"):
                        type_results["successful_extractions"] += 1
                        type_results["total_entities"] += result.get("total_entities", 0)
                        type_results["total_relationships"] += result.get("total_relationships", 0)
                    
                    type_results["sample_results"].append({
                        "feedback_id": sample["feedback_id"],
                        "success": result.get("success", False),
                        "entities": result.get("total_entities", 0),
                        "relationships": result.get("total_relationships", 0)
                    })
                    
                except Exception as e:
                    logger.error(f"Error testing {feedback_type} sample {sample['feedback_id']}: {e}")
                    type_results["sample_results"].append({
                        "feedback_id": sample["feedback_id"],
                        "success": False,
                        "error": str(e)
                    })
            
            results[feedback_type] = type_results
            logger.info(f"   ✅ {feedback_type}: {type_results['successful_extractions']}/{type_results['total_samples']} successful")
        
        return results
    
    def generate_test_report(self, test_results: Dict[str, Any]) -> str:
        """Generate a comprehensive test report."""
        report = f"""
# Customer Feedback Classification Test Report

**Test Date:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**System:** enterprise_kg_minimal
**LLM Provider:** {self.llm_provider}
**LLM Model:** {self.llm_model}

## Test Summary

- **Total Tests:** {test_results['total_tests']}
- **Successful Tests:** {test_results['successful_tests']}
- **Failed Tests:** {test_results['failed_tests']}
- **Success Rate:** {(test_results['successful_tests'] / test_results['total_tests'] * 100):.1f}%

## Entity & Relationship Extraction

- **Entity Extraction Success:** {test_results.get('entity_extraction_success', 0)} tests
- **Relationship Extraction Success:** {test_results.get('relationship_extraction_success', 0)} tests
- **Entity Extraction Rate:** {(test_results.get('entity_extraction_rate', 0) * 100):.1f}%
- **Relationship Extraction Rate:** {(test_results.get('relationship_extraction_rate', 0) * 100):.1f}%

## Detailed Results

"""
        
        for result in test_results['test_results']:
            report += f"""
### {result['feedback_id']} - {result['customer_name']}
- **Expected Sentiment:** {result['expected_sentiment']}
- **Priority:** {result['priority_level']}
- **Success:** {result['success']}
- **Entities Found:** {result['entities_found']}
- **Relationships Found:** {result['relationships_found']}
"""
            if result.get('processing_error'):
                report += f"- **Error:** {result['processing_error']}\n"
        
        return report

def main():
    """Main test execution function."""
    print("🧪 Customer Feedback Classification Test Suite")
    print("=" * 50)
    
    # Initialize tester
    tester = FeedbackClassificationTester()
    
    try:
        # Run comprehensive tests
        print("\n📋 Running comprehensive feedback classification tests...")
        comprehensive_results = tester.run_comprehensive_test()
        
        # Run specific type tests
        print("\n🎯 Running specific feedback type tests...")
        type_results = tester.test_specific_feedback_types()
        
        # Generate and display report
        print("\n📊 Generating test report...")
        report = tester.generate_test_report(comprehensive_results)
        
        # Save report to file
        report_filename = f"feedback_classification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(report_filename, 'w') as f:
            f.write(report)
        
        print(f"\n✅ Test completed successfully!")
        print(f"📄 Report saved to: {report_filename}")
        print(f"🎯 Success Rate: {(comprehensive_results['successful_tests'] / comprehensive_results['total_tests'] * 100):.1f}%")
        
        # Display summary
        print(f"\n📈 Summary:")
        print(f"   Total feedback samples tested: {comprehensive_results['total_tests']}")
        print(f"   Successful extractions: {comprehensive_results['successful_tests']}")
        print(f"   Entity extraction success: {comprehensive_results.get('entity_extraction_success', 0)}")
        print(f"   Relationship extraction success: {comprehensive_results.get('relationship_extraction_success', 0)}")
        
    except Exception as e:
        print(f"❌ Test suite failed: {str(e)}")
        logger.error(f"Test suite error: {e}", exc_info=True)
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
