"""
Relationship type definitions for Enterprise KG

This module defines all supported relationship types in the enterprise knowledge graph.
Add new relationship types here as the system evolves.
"""

from enum import Enum
from typing import Set, Dict, List, Tuple


class RelationshipType(Enum):
    """
    Enumeration of all supported relationship types in the enterprise knowledge graph.

    Each relationship type represents a distinct type of connection between entities.
    """

    # Basic relationships (your initial requirements)
    INVOLVED_IN = "involved_in"
    MENTIONS = "mentions"

    # Organizational relationships
    WORKS_FOR = "works_for"
    MANAGES = "manages"
    REPORTS_TO = "reports_to"
    LEADS = "leads"
    MEMBER_OF = "member_of"
    BELONGS_TO = "belongs_to"
    PART_OF = "part_of"

    # Project relationships
    OWNS = "owns"
    RESPONSIBLE_FOR = "responsible_for"
    PARTICIPATES_IN = "participates_in"
    CONTRIBUTES_TO = "contributes_to"
    DEPENDS_ON = "depends_on"
    BLOCKS = "blocks"
    ENABLES = "enables"

    # Document relationships
    AUTHORED_BY = "authored_by"
    REVIEWED_BY = "reviewed_by"
    APPROVED_BY = "approved_by"
    REFERENCES = "references"
    CITES = "cites"
    SUPERSEDES = "supersedes"
    IMPLEMENTS = "implements"

    # Business relationships
    COLLABORATES_WITH = "collaborates_with"
    COMPETES_WITH = "competes_with"
    PARTNERS_WITH = "partners_with"
    SUPPLIES = "supplies"
    PURCHASES_FROM = "purchases_from"
    CONTRACTS_WITH = "contracts_with"

    # Technical relationships
    USES = "uses"
    INTEGRATES_WITH = "integrates_with"
    CONNECTS_TO = "connects_to"
    HOSTS = "hosts"
    RUNS_ON = "runs_on"
    ACCESSES = "accesses"

    # Temporal relationships
    PRECEDES = "precedes"
    FOLLOWS = "follows"
    SCHEDULED_FOR = "scheduled_for"
    COMPLETED_ON = "completed_on"
    STARTED_ON = "started_on"

    # Financial relationships
    FUNDS = "funds"
    COSTS = "costs"
    GENERATES = "generates"
    BUDGETS_FOR = "budgets_for"

    # Location relationships
    LOCATED_IN = "located_in"
    OPERATES_IN = "operates_in"
    BASED_IN = "based_in"

    # Hierarchical relationships
    PARENT_OF = "parent_of"
    CHILD_OF = "child_of"
    CONTAINS = "contains"
    CONTAINED_IN = "contained_in"

    # Communication relationships
    COMMUNICATES_WITH = "communicates_with"
    REPORTS_TO_STAKEHOLDER = "reports_to"
    INFORMS = "informs"
    NOTIFIES = "notifies"

    # Generic relationships
    RELATED_TO = "related_to"
    ASSOCIATED_WITH = "associated_with"

    # File-to-knowledge relationships (for organizational integration)
    FILE_CONTAINS = "file_contains"          # File contains Entity (different from hierarchical contains)
    EXTRACTED_FROM = "extracted_from"        # Entity extracted from File
    MENTIONED_IN = "mentioned_in"           # Entity mentioned in File
    SOURCED_FROM = "sourced_from"           # Relationship sourced from File
    DOCUMENTED_IN = "documented_in"         # Process/Project documented in File

    # Customer Feedback relationships
    PROVIDES_FEEDBACK = "provides_feedback"  # Customer provides feedback
    EXPRESSES_SENTIMENT = "expresses_sentiment"  # Feedback expresses sentiment
    RELATES_TO_PRODUCT = "relates_to_product"  # Feedback relates to product
    RATES = "rates"                         # Customer rates product/service
    COMPLAINS_ABOUT = "complains_about"     # Customer complains about issue
    PRAISES = "praises"                     # Customer praises aspect
    REVIEWS = "reviews"                     # Customer reviews product/service
    EXPERIENCES = "experiences"             # Customer experiences issue/benefit


# Helper functions for relationship type management
def get_all_relationship_types() -> Set[str]:
    """Get all relationship type values as strings."""
    return {rel_type.value for rel_type in RelationshipType}


def get_organizational_relationships() -> Set[str]:
    """Get relationship types related to organizational structure."""
    return {
        RelationshipType.WORKS_FOR.value,
        RelationshipType.MANAGES.value,
        RelationshipType.REPORTS_TO.value,
        RelationshipType.LEADS.value,
        RelationshipType.MEMBER_OF.value,
        RelationshipType.BELONGS_TO.value,
        RelationshipType.PART_OF.value,
    }


def get_project_relationships() -> Set[str]:
    """Get relationship types related to projects."""
    return {
        RelationshipType.INVOLVED_IN.value,
        RelationshipType.OWNS.value,
        RelationshipType.RESPONSIBLE_FOR.value,
        RelationshipType.PARTICIPATES_IN.value,
        RelationshipType.CONTRIBUTES_TO.value,
        RelationshipType.DEPENDS_ON.value,
        RelationshipType.BLOCKS.value,
        RelationshipType.ENABLES.value,
    }


def get_document_relationships() -> Set[str]:
    """Get relationship types related to documents."""
    return {
        RelationshipType.AUTHORED_BY.value,
        RelationshipType.REVIEWED_BY.value,
        RelationshipType.APPROVED_BY.value,
        RelationshipType.REFERENCES.value,
        RelationshipType.CITES.value,
        RelationshipType.SUPERSEDES.value,
        RelationshipType.IMPLEMENTS.value,
        RelationshipType.MENTIONS.value,
        # File-to-knowledge relationships
        RelationshipType.FILE_CONTAINS.value,
        RelationshipType.EXTRACTED_FROM.value,
        RelationshipType.MENTIONED_IN.value,
        RelationshipType.SOURCED_FROM.value,
        RelationshipType.DOCUMENTED_IN.value,
    }


def get_feedback_relationships() -> Set[str]:
    """Get relationship types related to customer feedback and sentiment."""
    return {
        RelationshipType.PROVIDES_FEEDBACK.value,
        RelationshipType.EXPRESSES_SENTIMENT.value,
        RelationshipType.RELATES_TO_PRODUCT.value,
        RelationshipType.RATES.value,
        RelationshipType.COMPLAINS_ABOUT.value,
        RelationshipType.PRAISES.value,
        RelationshipType.REVIEWS.value,
        RelationshipType.EXPERIENCES.value,
    }


def is_valid_relationship_type(relationship_type: str) -> bool:
    """Check if a string is a valid relationship type."""
    return relationship_type in get_all_relationship_types()


def get_inverse_relationship(relationship_type: RelationshipType) -> RelationshipType:
    """Get the inverse relationship type if it exists."""
    inverse_mapping = {
        RelationshipType.MANAGES: RelationshipType.REPORTS_TO,
        RelationshipType.REPORTS_TO: RelationshipType.MANAGES,
        RelationshipType.PARENT_OF: RelationshipType.CHILD_OF,
        RelationshipType.CHILD_OF: RelationshipType.PARENT_OF,
        RelationshipType.CONTAINS: RelationshipType.CONTAINED_IN,
        RelationshipType.CONTAINED_IN: RelationshipType.CONTAINS,
        RelationshipType.PRECEDES: RelationshipType.FOLLOWS,
        RelationshipType.FOLLOWS: RelationshipType.PRECEDES,
        RelationshipType.SUPPLIES: RelationshipType.PURCHASES_FROM,
        RelationshipType.PURCHASES_FROM: RelationshipType.SUPPLIES,
    }

    return inverse_mapping.get(relationship_type, relationship_type)


def get_relationship_description(relationship_type: RelationshipType) -> str:
    """Get a human-readable description for a relationship type."""
    descriptions = {
        RelationshipType.INVOLVED_IN: "Entity is involved in or participates in another entity",
        RelationshipType.MENTIONS: "Entity mentions or references another entity",

        RelationshipType.WORKS_FOR: "Person works for an organization",
        RelationshipType.MANAGES: "Person manages another person or entity",
        RelationshipType.REPORTS_TO: "Person reports to another person",
        RelationshipType.LEADS: "Person leads a team, project, or initiative",
        RelationshipType.MEMBER_OF: "Entity is a member of a group or organization",
        RelationshipType.BELONGS_TO: "Entity belongs to another entity",
        RelationshipType.PART_OF: "Entity is part of a larger entity",

        RelationshipType.OWNS: "Entity owns or has ownership of another entity",
        RelationshipType.RESPONSIBLE_FOR: "Entity is responsible for another entity",
        RelationshipType.PARTICIPATES_IN: "Entity participates in an activity or project",
        RelationshipType.CONTRIBUTES_TO: "Entity contributes to another entity",
        RelationshipType.DEPENDS_ON: "Entity depends on another entity",
        RelationshipType.BLOCKS: "Entity blocks or prevents another entity",
        RelationshipType.ENABLES: "Entity enables or facilitates another entity",

        RelationshipType.AUTHORED_BY: "Document was authored by a person",
        RelationshipType.REVIEWED_BY: "Document was reviewed by a person",
        RelationshipType.APPROVED_BY: "Document was approved by a person",
        RelationshipType.REFERENCES: "Entity references another entity",
        RelationshipType.CITES: "Entity cites another entity",
        RelationshipType.SUPERSEDES: "Entity supersedes or replaces another entity",
        RelationshipType.IMPLEMENTS: "Entity implements another entity",

        RelationshipType.COLLABORATES_WITH: "Entity collaborates with another entity",
        RelationshipType.COMPETES_WITH: "Entity competes with another entity",
        RelationshipType.PARTNERS_WITH: "Entity partners with another entity",
        RelationshipType.SUPPLIES: "Entity supplies goods/services to another entity",
        RelationshipType.PURCHASES_FROM: "Entity purchases from another entity",
        RelationshipType.CONTRACTS_WITH: "Entity has a contract with another entity",

        RelationshipType.USES: "Entity uses another entity",
        RelationshipType.INTEGRATES_WITH: "Entity integrates with another entity",
        RelationshipType.CONNECTS_TO: "Entity connects to another entity",
        RelationshipType.HOSTS: "Entity hosts another entity",
        RelationshipType.RUNS_ON: "Entity runs on another entity",
        RelationshipType.ACCESSES: "Entity accesses another entity",

        RelationshipType.PRECEDES: "Entity comes before another entity in time",
        RelationshipType.FOLLOWS: "Entity comes after another entity in time",
        RelationshipType.SCHEDULED_FOR: "Entity is scheduled for a specific time",
        RelationshipType.COMPLETED_ON: "Entity was completed on a specific date",
        RelationshipType.STARTED_ON: "Entity started on a specific date",

        RelationshipType.FUNDS: "Entity provides funding for another entity",
        RelationshipType.COSTS: "Entity has a cost associated with another entity",
        RelationshipType.GENERATES: "Entity generates revenue or value",
        RelationshipType.BUDGETS_FOR: "Entity allocates budget for another entity",

        RelationshipType.LOCATED_IN: "Entity is located in a place",
        RelationshipType.OPERATES_IN: "Entity operates in a location",
        RelationshipType.BASED_IN: "Entity is based in a location",

        RelationshipType.PARENT_OF: "Entity is the parent of another entity",
        RelationshipType.CHILD_OF: "Entity is the child of another entity",
        RelationshipType.CONTAINS: "Entity contains another entity",
        RelationshipType.CONTAINED_IN: "Entity is contained in another entity",

        RelationshipType.COMMUNICATES_WITH: "Entity communicates with another entity",
        RelationshipType.REPORTS_TO_STAKEHOLDER: "Entity reports to a stakeholder",
        RelationshipType.INFORMS: "Entity informs another entity",
        RelationshipType.NOTIFIES: "Entity notifies another entity",

        RelationshipType.RELATED_TO: "Entity is related to another entity",
        RelationshipType.ASSOCIATED_WITH: "Entity is associated with another entity",

        # Customer Feedback relationships
        RelationshipType.PROVIDES_FEEDBACK: "Customer provides feedback about a product or service",
        RelationshipType.EXPRESSES_SENTIMENT: "Feedback expresses a particular sentiment",
        RelationshipType.RELATES_TO_PRODUCT: "Feedback relates to a specific product or service",
        RelationshipType.RATES: "Customer rates a product or service",
        RelationshipType.COMPLAINS_ABOUT: "Customer complains about an issue",
        RelationshipType.PRAISES: "Customer praises an aspect or feature",
        RelationshipType.REVIEWS: "Customer reviews a product or service",
        RelationshipType.EXPERIENCES: "Customer experiences an issue or benefit",
    }

    return descriptions.get(relationship_type, "Unknown relationship type")


def get_relationship_category_mapping() -> Dict[str, callable]:
    """
    Get mapping of category names to their corresponding getter functions.
    This centralizes all relationship categorization logic in one place.

    To add a new category:
    1. Create a new get_[category]_relationships() function above
    2. Add it to this mapping

    Returns:
        Dictionary mapping category names to getter functions
    """
    return {
        "Project Relationships": get_project_relationships,
        "Organizational Relationships": get_organizational_relationships,
        "Document Relationships": get_document_relationships,
        "Customer Feedback Relationships": get_feedback_relationships,
        # Add new categories here as needed
        # "Technical Relationships": get_technical_relationships,
        # "Financial Relationships": get_financial_relationships,
    }


def get_common_entity_relationship_patterns() -> List[Tuple[str, str, str]]:
    """
    Get common entity-relationship patterns for the initial setup.
    Returns list of (subject_entity_type, relationship_type, object_entity_type) tuples.
    """
    return [
        # Your initial requirements
        ("Person", "involved_in", "Project"),
        ("Project", "mentions", "Person"),

        # Common organizational patterns
        ("Person", "works_for", "Company"),
        ("Person", "manages", "Team"),
        ("Person", "reports_to", "Manager"),
        ("Employee", "member_of", "Department"),

        # Project patterns
        ("Person", "owns", "Project"),
        ("Person", "responsible_for", "Initiative"),
        ("Project", "depends_on", "System"),
        ("Team", "participates_in", "Program"),

        # Document patterns
        ("Document", "authored_by", "Person"),
        ("Report", "reviewed_by", "Manager"),
        ("Proposal", "approved_by", "Executive"),
        ("Contract", "references", "Policy"),

        # Technology patterns
        ("Application", "runs_on", "Platform"),
        ("System", "integrates_with", "Database"),
        ("Tool", "used_by", "Team"),

        # Location patterns
        ("Person", "located_in", "Office"),
        ("Team", "based_in", "Region"),
        ("Company", "operates_in", "Country"),

        # Customer Feedback patterns
        ("Customer", "provides_feedback", "Product"),
        ("Customer", "rates", "Service"),
        ("Feedback", "expresses_sentiment", "Sentiment"),
        ("Feedback", "relates_to_product", "Product"),
        ("Customer", "complains_about", "Product"),
        ("Customer", "praises", "Service"),
        ("Customer", "reviews", "Product"),
        ("Customer", "experiences", "Complaint"),
    ]
