import os
from dotenv import load_dotenv
from enterprise_kg_minimal import search_knowledge_graph
from enterprise_kg_minimal.search.search_schemas import SearchStrategy

# Load environment variables from .env file in the enterprise_kg_minimal directory
# Assuming this script is run from the workspace root, and .env is inside enterprise_kg_minimal
dotenv_path = os.path.join("enterprise_kg_minimal", ".env")
load_dotenv(dotenv_path)

NEO4J_URI = os.getenv("NEO4J_URI")
NEO4J_USER = os.getenv("NEO4J_USER")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD")

print(f"Attempting to connect to Neo4j: URI='{NEO4J_URI}', User='{NEO4J_USER}'")

dummy_chunk_indices = ["test_doc_chunk_0_dummy", "test_doc_chunk_1_dummy"]
dummy_query = "information about AI projects"

try:
    result = search_knowledge_graph(
        chunk_indices=dummy_chunk_indices,
        query_text=dummy_query,
        neo4j_uri=NEO4J_URI,
        neo4j_user=NEO4J_USER,
        neo4j_password=NEO4J_PASSWORD,
        strategy=SearchStrategy.HYBRID.value,
        max_results=5,
        expansion_depth=1
    )

    print("\nSearch Result:")
    if result.get("success"):
        print(f"  Success: True")
        print(f"  Total results: {result.get('total_results')}")
        print(f"  Processing time: {result.get('processing_time_ms', 0):.2f}ms")
        print(f"  Strategy used: {result.get('strategy_used')}")
        
        entities = result.get("entities", [])
        print(f"  Entities found: {len(entities)}")
        for entity in entities[:2]: # Print first 2 entities
            print(f"    - Name: {entity.get('name')}, Type: {entity.get('type')}, Score: {entity.get('relevance_score')}")
        
        relationships = result.get("relationships", [])
        print(f"  Relationships found: {len(relationships)}")
        for rel in relationships[:2]: # Print first 2 relationships
            print(f"    - Source: {rel.get('source')}, Type: {rel.get('type')}, Target: {rel.get('target')}, Score: {rel.get('relevance_score')}")
        
        if not entities and not relationships:
            print("  Note: No entities or relationships found. This is expected if dummy chunk IDs do not match actual data in the graph.")
    else:
        print(f"  Success: False")
        print(f"  Error: {result.get('error')}")

except Exception as e:
    print(f"\nAn error occurred during the test: {str(e)}")