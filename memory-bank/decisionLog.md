# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-05 11:44:20 - Updated during memory bank population task.

*

## Decision (2025-06-05)

*   **Decision:** Populate the memory bank files (`productContext.md`, `activeContext.md`, `progress.md`, `decisionLog.md`, `systemPatterns.md`) by systematically extracting and summarizing information from the `enterprise_kg_minimal` project's documentation (READMEs) and code structure (module definitions).
*   **Rationale:** To ensure the memory bank files accurately reflect the `enterprise_kg_minimal` project's purpose, architecture, key features, and established patterns. This provides a reliable and up-to-date contextual foundation for any future work or understanding of the project.
*   **Implementation Details:**
    *   Information was sourced primarily from [`enterprise_kg_minimal/README.md`](enterprise_kg_minimal/README.md), [`enterprise_kg_minimal/search/README.md`](enterprise_kg_minimal/search/README.md), and insights derived from the `list_code_definition_names` output for the `core` and `search` modules.
    *   Each memory bank file was updated according to the specific sections outlined in the `memory-bank/update_plan.md` document.
    *   The process involved reading project files, analyzing code definitions, and then writing the summarized information into the respective memory bank files.
---
[2025-06-05 13:19:00] - **Decision**: Made relevance score calculation weights in `GraphRAG` configurable.
**Rationale**: To allow for easier tuning and experimentation with different scoring heuristics without direct code modification. This enhances flexibility for optimizing search relevance across various datasets and query types.
**Implementation Details**:
    - Added new float fields to `SearchQuery` in `enterprise_kg_minimal/search/search_schemas.py` for weights related to:
        - Initial entity query relevance boost.
        - Neighbor entity depth penalty and confidence boost (traditional).
        - Hybrid neighbor entity query relevance and depth penalty.
        - Query relevance components (overlap, semantic, type).
        - Keyword overlap components (Jaccard, coverage).
    - Updated methods in `enterprise_kg_minimal/search/graph_rag.py` (`_extract_entities_from_chunks`, `_find_neighboring_entities`, `_find_neighboring_entities_hybrid`, `_calculate_query_entity_relevance`, `_calculate_keyword_overlap`) to use these weights from the `SearchQuery` object instead of hardcoded values.