# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-06-05 11:44:30 - Updated with patterns observed in `enterprise_kg_minimal`.

*

## Coding Patterns

*   **Modular Design:** Clear separation of concerns into distinct directories like `core/`, `llm/`, `storage/`, `search/`, `constants/`, and `utils/` within the `enterprise_kg_minimal` package. This promotes maintainability and scalability.
*   **Data Structures (`dataclasses`):** Extensive use of Python's `dataclasses` for creating structured data objects (e.g., `ChunkMetadata`, `DocumentChunk` in [`enterprise_kg_minimal/core/chunking_engine.py`](enterprise_kg_minimal/core/chunking_engine.py); `SearchQuery`, `EntityMatch`, `RelationshipMatch` in [`enterprise_kg_minimal/search/search_schemas.py`](enterprise_kg_minimal/search/search_schemas.py)). This enhances code readability and type safety.
*   **Enumerations (`Enum`):** Utilization of `Enum` for defining predefined sets of choices, such as `ChunkingStrategy` ([`enterprise_kg_minimal/core/chunking_engine.py`](enterprise_kg_minimal/core/chunking_engine.py)) and `SearchStrategy` ([`enterprise_kg_minimal/search/search_schemas.py`](enterprise_kg_minimal/search/search_schemas.py)), improving clarity and preventing errors from magic strings/values.
*   **Centralized Constants:** Entity types, relationship types, and other schema-related definitions are managed centrally within the [`enterprise_kg_minimal/constants/`](enterprise_kg_minimal/constants/) directory, making the system configurable and easier to update.
*   **Facade Functions:** Key functionalities are exposed through simplified, high-level interfaces. Examples include `process_document()` in [`enterprise_kg_minimal/core/document_processor.py`](enterprise_kg_minimal/core/document_processor.py) and `search_with_chunk_indices()` in [`enterprise_kg_minimal/search/hybrid_search_engine.py`](enterprise_kg_minimal/search/hybrid_search_engine.py).
*   **Factory/Builder Functions:** Functions are used to create and configure instances of complex objects, such as `create_hybrid_search_engine()` in [`enterprise_kg_minimal/search/hybrid_search_engine.py`](enterprise_kg_minimal/search/hybrid_search_engine.py) and `create_basic_prompt_generator()` in [`enterprise_kg_minimal/core/prompt_generator.py`](enterprise_kg_minimal/core/prompt_generator.py).
*   **Comprehensive Error Handling:** Consistent use of `try-except` blocks throughout the codebase to manage potential errors gracefully and return detailed error information within the results (e.g., in `process_document()` and search operations).
*   **Logging:** Standard Python `logging` is used for tracking events, errors, and informational messages.

## Architectural Patterns

*   **Pipeline Architecture:** Document ingestion follows a clear pipeline: Document Input → Chunking → Entity/Relationship Extraction (via LLM) → Graph Construction → Storage in Neo4j.
*   **Chunk-Based Knowledge Graph:** The core architectural principle involves breaking down source documents into smaller, manageable chunks. These chunks then form the basis for extracting entities and relationships, creating a granular knowledge graph.
*   **Hybrid Search:** The search functionality combines traditional vector similarity search (assumed to be handled by an external system like Pinecone, providing chunk indices) with Graph-based Retrieval Augmented Generation (GraphRAG). This leverages both semantic similarity and explicit graph connections.
*   **Strategy Pattern:** Different search algorithms and approaches are implemented as separate strategies (e.g., `EntityCentricStrategy`, `RelationshipCentricStrategy`, `ChunkExpansionStrategy`, `HierarchicalStrategy` found in [`enterprise_kg_minimal/search/search_strategies.py`](enterprise_kg_minimal/search/search_strategies.py)). The `HybridSearchEngine` can then select or combine these strategies.
*   **Service Abstraction:** Components like `Neo4jClient` ([`enterprise_kg_minimal/storage/neo4j_client.py`](enterprise_kg_minimal/storage/neo4j_client.py)) and `LLMClient` ([`enterprise_kg_minimal/llm/client.py`](enterprise_kg_minimal/llm/client.py)) abstract the interactions with external services (database, LLM providers), making the core logic independent of specific implementations.
*   **Result Aggregation:** The `SearchResultAggregator` ([`enterprise_kg_minimal/search/result_aggregator.py`](enterprise_kg_minimal/search/result_aggregator.py)) is responsible for combining, deduplicating, and ranking results from potentially multiple search strategies or expansion paths.

## Testing Patterns

*   **Dedicated Test Files:** The system includes specific test scripts, such as [`test_enterprise_kg_minimal.py`](test_enterprise_kg_minimal.py) (located outside the `enterprise_kg_minimal` package but tests its functionality) and [`enterprise_kg_minimal/search/test_hybrid_search.py`](enterprise_kg_minimal/search/test_hybrid_search.py).
*   **Comprehensive Test Coverage (Implied):** The presence of test files suggests an aim for good test coverage, including component initialization, parameter validation, core functionalities (like various chunking methods, entity extraction logic, and different search strategies), and integration points between modules.
*   **Usage Examples as Informal Tests/Documentation:** Files like [`enterprise_kg_minimal/example_usage.py`](enterprise_kg_minimal/example_usage.py), [`enterprise_kg_minimal/search/example_usage.py`](enterprise_kg_minimal/search/example_usage.py), and [`enterprise_kg_minimal/search/complete_example.py`](enterprise_kg_minimal/search/complete_example.py) not only demonstrate how to use the package but also serve as informal test cases that can verify basic functionality.