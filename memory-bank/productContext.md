# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-06-05 11:43:35 - Updated with details from enterprise_kg_minimal project analysis.

*

## Project Goal

*   **Primary Goal:** To provide a clean, modular enterprise knowledge graph system that creates chunk-based knowledge graphs from documents. This system is designed to be production-ready and easily integrated into existing systems.
*   **Search Goal:** To offer a powerful hybrid search engine that combines vector similarity search with graph-based retrieval (GraphRAG) for enhanced information discovery within the generated enterprise knowledge graphs.

## Key Features

*   **📊 Chunk-Based Architecture**: Implements a File → Chunks → Entities graph structure for organizing knowledge.
*   **🔧 Simple API**: Exposes a single `process_document()` function interface for document ingestion.
*   **🧩 Advanced Chunking**: Supports multiple chunking strategies (e.g., hybrid, sentence-based, paragraph-based).
*   **🤖 LLM Integration**: Features multi-provider support for LLMs, enabling entity and relationship extraction.
*   **🗄️ Neo4j Storage**: Utilizes Neo4j as a scalable graph database with well-defined relationships.
*   **📦 Modular Design**: Characterized by a clean separation of concerns, enhancing maintainability.
*   **✅ Production Ready**: Includes comprehensive error handling and logging mechanisms.
*   **🚀 Hybrid Search**: Combines vector similarity search with graph traversal for enriched results.
*   **🎯 Multiple Search Strategies**: Offers various search approaches like Entity-centric, Relationship-centric, Chunk Expansion, and Hierarchical search.
*   **⚙️ Generic Design (Search)**: Dynamically loads all entity and relationship types from constants, avoiding hardcoding.
*   **🔗 Graph Enrichment (Search)**: Expands search context through relationship traversal in the knowledge graph.
*   **📈 Result Aggregation (Search)**: Intelligently deduplicates and ranks search results.
*   **🛡️ Type Validation (Search)**: Validates entity and relationship types against defined constants during search.
*   **⏱️ Performance Metrics (Search)**: Provides comprehensive timing and quality metrics for search operations.

## Overall Architecture

The `enterprise_kg_minimal` system is composed of several key modules and follows distinct pipelines for document ingestion and search.

### Module Structure:
*   `enterprise_kg_minimal/`:
    *   `__init__.py`: Main importable function (`process_document`).
    *   `core/`: Core processing modules.
        *   `document_processor.py`: Main document processing logic.
        *   `chunking_engine.py`: Handles document chunking.
        *   `graph_builder.py`: Constructs the graph in Neo4j.
        *   `prompt_generator.py`: Generates prompts for LLM interaction.
    *   `llm/`: LLM integration.
        *   `client.py`: LLM client for various providers.
    *   `storage/`: Data storage.
        *   `neo4j_client.py`: Operations for Neo4j database.
    *   `constants/`: Defines entities, relationships, and schemas.
    *   `utils/`: Utility functions.
    *   `search/`: Hybrid search engine components.
        *   `hybrid_search_engine.py`: Main search orchestration.
        *   `graph_rag.py`: Graph-based retrieval augmented generation.
        *   `search_strategies.py`: Different search algorithms.
        *   `result_aggregator.py`: Combines and optimizes search results.
        *   `search_schemas.py`: Data classes for search operations.

### Graph Structure:
The system creates a hierarchical graph:
1.  **File Node**: Represents the source document.
2.  **Chunk Nodes**: Individual chunks derived from the document.
3.  **Entity Nodes**: Entities extracted from each chunk.
4.  **Relationships**:
    *   `CONTAINS`: File → Chunk
    *   `EXTRACTED_FROM`: Chunk → Entity
    *   Domain-specific relationships between entities.

### Search Architecture:
Vector Search (e.g., Pinecone) → Chunk Indices → Hybrid Search Engine → GraphRAG Engine → Neo4j Knowledge Graph.

### System Interaction Diagram:

```mermaid
graph TD
    subgraph "Enterprise KG Minimal System"
        direction LR

        subgraph "Document Ingestion Pipeline"
            A[Source Document] --> B(core.document_processor.process_document)
            B --> C(core.chunking_engine.ChunkingEngine)
            C --> D[Document Chunks]
            D --> E(llm.client for Entity/Relationship Extraction)
            E --> F[Extracted Entities & Relationships]
            F --> G(core.graph_builder.GraphBuilder)
            G --> H[(storage.neo4j_client)]
            H --> I([Neo4j Graph Database])
        end

        subgraph "Hybrid Search Pipeline"
            J[User Query] --> K{Vector Search (e.g., Pinecone)}
            K --> L[Chunk Indices]
            L --> M(search.hybrid_search_engine.HybridSearchEngine)
            M --> N(search.graph_rag.GraphRAG)
            N --> H
            M --> O(search.search_strategies)
            M --> P(search.result_aggregator.SearchResultAggregator)
            P --> Q[Enriched Search Results]
        end

        subgraph "Supporting Modules"
            R[constants: Entities, Relationships, Schemas]
            S[utils: Helper functions]
        end

        B --> R
        E --> R
        G --> R
        M --> R
        N --> R
    end